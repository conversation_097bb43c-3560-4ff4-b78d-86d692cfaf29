﻿/* 毒蘑菇显卡测试工具 - 自定义样式 */

:root {
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #4338ca;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
}

/* 背景样式 */
body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f0f4ff 0%, #f9fafb 50%, #f0fdfa 100%);
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(79, 70, 229, 0.08) 0%, rgba(79, 70, 229, 0.02) 25%, transparent 50%),
        radial-gradient(circle at 80% 30%, rgba(16, 185, 129, 0.06) 0%, rgba(16, 185, 129, 0.01) 30%, transparent 55%),
        radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.01) 35%, transparent 60%);
    z-index: -1;
    pointer-events: none;
    opacity: 0.8;
}

.dark body {
    background: linear-gradient(135deg, #111827 0%, #1e293b 50%, #0f172a 100%);
}

.dark body::before {
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(79, 70, 229, 0.08) 0%, rgba(79, 70, 229, 0.03) 30%, transparent 60%),
        radial-gradient(circle at 80% 30%, rgba(6, 182, 212, 0.06) 0%, rgba(6, 182, 212, 0.02) 35%, transparent 65%),
        radial-gradient(circle at 70% 80%, rgba(124, 58, 237, 0.07) 0%, rgba(124, 58, 237, 0.02) 40%, transparent 70%);
    opacity: 0.7;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.75rem 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 250, 251, 0.95) 100%);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.dark header {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(55, 65, 81, 0.5);
}

/* 导航菜单样式 */
.nav-menu {
    display: flex;
    align-items: center;
    margin-left: 2rem;
    flex: 1;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 1rem;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    color: #374151;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.15s;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.15s;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: var(--primary-color);
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

/* 测试模式卡片样式 */
.test-modes-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.7rem;
    margin-top: 0;
}

.hide-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
}

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.test-mode-card {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: white;
    border-radius: 6px;
    padding: 0.5rem;
    text-decoration: none;
    color: #111827;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    border: 1px solid rgba(229, 231, 235, 0.5);
    justify-content: flex-start;
    transition: all 0.15s;
}

.dark .test-mode-card {
    background-color: #1f2937;
    color: #f9fafb;
    border-color: rgba(55, 65, 81, 0.5);
}

.test-mode-card:hover {
    color: #4f46e5;
    border-color: #4f46e5;
}

.test-mode-card.active {
    border-color: #4f46e5;
    color: #4f46e5;
}

.mode-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    min-width: 28px;
    border-radius: 50%;
    background-color: #f9fafb;
    margin-right: 0.5rem;
    font-size: 0.875rem;
    color: #4f46e5;
}

.dark .mode-icon {
    background-color: #1f2937;
}

.mode-info {
    flex-grow: 1;
    text-align: left;
}

.mode-info h3 {
    font-size: 0.8rem;
    font-weight: 600;
    margin: 0 0 0.1rem 0;
    color: #111827;
    line-height: 1.2;
}

.dark .mode-info h3 {
    color: #f9fafb;
}

.mode-info p {
    font-size: 0.65rem;
    margin: 0;
    color: #6b7280;
    line-height: 1.1;
}

.dark .mode-info p {
    color: #9ca3af;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(to right, #6366f1, #8b5cf6);
    color: white;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background: linear-gradient(to right, #4f46e5, #7c3aed);
}

.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
    border: 1px solid #d1d5db;
    cursor: pointer;
}

.dark .btn-secondary {
    background-color: #374151;
    color: #e5e7eb;
    border-color: #4b5563;
}

.btn-secondary:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

.dark .btn-secondary:hover {
    background-color: #4b5563;
    border-color: #6b7280;
}

.btn-danger {
    background-color: #fef2f2;
    color: #dc2626;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
    border: 1px solid #fecaca;
    cursor: pointer;
}

.dark .btn-danger {
    background-color: #1f2937;
    color: #ef4444;
    border-color: #374151;
}

.btn-danger:hover {
    background-color: #ef4444;
    color: white;
    border-color: #dc2626;
}

.btn-danger:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-radius: 8px;
    border: 1px solid rgba(229, 231, 235, 0.5);
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .card {
    background: rgba(31, 41, 55, 0.7);
    border: 1px solid rgba(55, 65, 81, 0.5);
}

/* 图表标签样式 */
.chart-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
    background-color: #f3f4f6;
    color: #374151;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
}

.dark .chart-tab {
    background-color: #374151;
    color: #e5e7eb;
}

.chart-tab.active {
    background-color: #ddd6fe;
    color: #6d28d9;
}

.dark .chart-tab.active {
    background-color: rgba(139, 92, 246, 0.2);
    color: #a78bfa;
}

.chart-tab:hover {
    background-color: #e5e7eb;
}

.dark .chart-tab:hover {
    background-color: #4b5563;
}

/* 主题切换按钮 */
.theme-toggle {
    cursor: pointer;
    font-size: 1rem;
    color: #6b7280;
    transition: all 0.15s;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: transparent;
}

.theme-toggle:hover {
    color: #4f46e5;
    background-color: rgba(79, 70, 229, 0.05);
}

/* 文本渐变效果 */
.text-gradient {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
}

/* 范围滑块样式 */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
    background: #d1d5db;
    height: 8px;
    border-radius: 4px;
}

.dark input[type="range"]::-webkit-slider-track {
    background: #374151;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #4f46e5;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-moz-range-track {
    background: #d1d5db;
    height: 8px;
    border-radius: 4px;
    border: none;
}

.dark input[type="range"]::-moz-range-track {
    background: #374151;
}

input[type="range"]::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #4f46e5;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 性能HUD样式 */
#performance-hud {
    font-family: 'Courier New', monospace;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* 警告覆盖层动画 */
#warning-overlay {
    animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 0.7; }
}

/* 响应式样式 */
@media (max-width: 767px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        margin-left: 0;
        background: #ffffff;
        padding: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 99;
        border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    }
    
    .dark .nav-menu {
        background: #111827;
        border-bottom: 1px solid rgba(55, 65, 81, 0.5);
    }
    
    .nav-menu.active {
        display: block;
    }
    
    .nav-menu ul {
        flex-direction: column;
    }
    
    .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: transparent;
        cursor: pointer;
        color: #6b7280;
        transition: all 0.15s;
        border: 1px solid transparent;
    }
    
    .mobile-menu-btn:hover {
        color: #4f46e5;
        background-color: rgba(79, 70, 229, 0.05);
        border-color: rgba(79, 70, 229, 0.1);
    }
    
    .test-mode-card {
        padding: 0.4rem;
    }
    
    .mode-icon {
        width: 24px;
        height: 24px;
        min-width: 24px;
        font-size: 0.75rem;
    }
    
    .mode-info h3 {
        font-size: 0.75rem;
    }
    
    .mode-info p {
        font-size: 0.6rem;
    }
}

/* 空状态样式 */
.empty-log {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    padding: 1rem;
    text-align: center;
}

.dark .empty-log {
    color: #9ca3af;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 移动端测试模式卡片样式 */
.mobile-test-mode-card {
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-test-mode-card:hover .bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.15) !important;
}

.mobile-test-mode-card.active .bg-white\/10 {
    background-color: rgba(79, 70, 229, 0.2) !important;
    border-color: rgba(79, 70, 229, 0.5) !important;
}

.mobile-test-mode-card.active h4 {
    color: #a5b4fc !important;
}

.mobile-test-mode-card.active p {
    color: #ddd6fe !important;
}

.mobile-test-mode-card.active .text-blue-400,
.mobile-test-mode-card.active .text-yellow-400,
.mobile-test-mode-card.active .text-orange-400,
.mobile-test-mode-card.active .text-red-400 {
    color: #ffffff !important;
} 