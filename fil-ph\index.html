<!DOCTYPE html>
<html lang="fil">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM Test Website - Online GPU Performance Benchmark Tool</title>
    <meta name="description" content="Propesyonal na online GPU performance testing tool na sumusukat sa performance ng graphics card sa pamamagitan ng complex 3D volume shader rendering, sumusuporta sa real-time FPS monitoring at performance analysis.">
    <meta name="keywords" content="graphics card test,GPU test,Toxic Mushroom test,volumeshader,WebGL,FPS test">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/">
    <link rel="icon" href="logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="logo.svg">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/">
    <meta property="og:title" content="Volume Shader BM Test | Online GPU Performance Benchmark Tool">
    <meta property="og:description" content="Propesyonal na online GPU performance testing tool na sumusukat sa performance ng graphics card sa pamamagitan ng complex 3D volume shader rendering, sumusuporta sa real-time FPS monitoring at performance analysis.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/">
    <meta property="twitter:title" content="Volume Shader BM Test | Online GPU Performance Benchmark Tool">
    <meta property="twitter:description" content="Propesyonal na online GPU performance testing tool na sumusukat sa performance ng graphics card sa pamamagitan ng complex 3D volume shader rendering, sumusuporta sa real-time FPS monitoring at performance analysis.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/twitter-image.jpg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/#website",
                "url": "https://volumeshaderbmtest.com/",
                "name": "Volume Shader BM Test",
                "description": "Propesyonal na online GPU performance benchmark tool",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/#organization",
                "name": "Toxic Mushroom Test Team",
                "url": "https://volumeshaderbmtest.com/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/#software",
                "name": "Volume Shader BM Test",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/",
                "description": "WebGL-based na online GPU performance benchmark tool, sumusuporta sa apat na test modes: light, medium, heavy, at extreme, may real-time monitoring ng FPS, temperature, at iba pang mahahalagang performance metrics.",
                "featureList": [
                    "GPU Performance Testing",
                    "Real-time FPS Monitoring",
                    "Temperature Status Monitoring",
                    "Performance History Recording",
                    "Test Result Analysis",
                    "WebGL Volume Rendering",
                    "Multiple Difficulty Test Modes"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "CNY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/#gpu-knowledge",
                "headline": "GPU Knowledge Base - Kumpletong Graphics Card Technical Guide",
                "description": "Detalyadong pagpapakilala sa GPU basics, mga pangunahing parameter, architecture development, at mga salik na nakakaapekto sa performance, nagbibigay ng propesyonal na gabay para sa graphics card selection at performance optimization.",
                "author": {
                    "@type": "Organization",
                    "name": "Toxic Mushroom Test Team"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "Why does my device heat up during testing?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "This is normal. Toxic Mushroom test makes the GPU work at full capacity, and heat generation is unavoidable. If the temperature is too high, it is recommended to stop the test or lower the test level."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "What does the score in the test results represent?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "The score takes into account FPS, rendering stability, and test level. A higher score represents stronger GPU performance and can be compared horizontally with other devices."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "What should I do if my browser shows WebGL is not supported?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Please make sure your browser is the latest version, check if hardware acceleration is enabled, or try switching browsers. Some mobile devices may not support WebGL 2.0."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Will the test data be uploaded?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "All test data is saved locally and will not be uploaded to any server. You can safely conduct tests without worrying about privacy issues."
                        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/#gpu-recommendations",
                "name": "Graphics Card Recommendation Guide",
                "description": "Professional graphics card recommendations categorized by budget and purpose",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
                        "description": "Entry-level gaming graphics card, suitable for 1080p high-quality gaming",
                        "category": "Graphics Card",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1500",
                            "highPrice": "2000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
                        "description": "Best value choice, recommended entry-level graphics card",
                        "category": "Graphics Card",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1400",
                            "highPrice": "1800",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
                        "description": "Mid-range gaming graphics card, supports 1440p high-quality gaming",
                        "category": "Graphics Card",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "3000",
                            "highPrice": "3500",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 4,
                        "name": "NVIDIA GeForce RTX 4090",
                        "description": "Flagship graphics card, ultimate 4K gaming performance",
                        "category": "Graphics Card",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "12000",
                            "highPrice": "15000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/#test-guide",
                "name": "GPU Performance Testing Guide",
                "description": "Detailed GPU performance testing steps and best practices",
                "image": "https://volumeshaderbmtest.com/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "WebGL-compatible browser"
                    },
                    {
                        "@type": "HowToSupply", 
                        "name": "Dedicated or integrated graphics card"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "Volume Shader BM Testing Tool"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "Paghahanda bago mag-test",
                        "text": "Isara ang mga hindi kinakailangang programa at browser tabs, siguraduhing may magandang heat dissipation environment ang device"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "Pumili ng test mode",
                        "text": "Pumili ng naaangkop na test mode ayon sa device performance, inirerekumenda na magsimula sa light mode"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "Simulan ang testing",
                        "text": "I-click ang start test at obserbahan ang real-time performance data changes"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "Tingnan ang mga resulta",
                        "text": "Tingnan ang detalyadong mga ulat at performance recommendations pagkatapos ng test"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="static/js/js.js"></script>
    <script src="static/js/chart.js"></script>
    <script src="static/js/three.min.js"></script>
    <link rel="stylesheet" href="static/css/all.min.css">
    <link href="static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="logo.svg" alt="Logo" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader BM Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="#test" class="active nav-link">Performance Test</a></li>
                    <li><a href="#about" class="nav-link">Tungkol sa Tool</a></li>
                    <li><a href="#test-guide" class="nav-link">Test Guide</a></li>
                    <li><a href="#technology" class="nav-link">Technical Principles</a></li>
                    <!-- <li><a href="#compatibility" class="nav-link">Compatibility</a></li>
                    <li><a href="#gpu-knowledge" class="nav-link">GPU Knowledge</a></li>
                    <li><a href="#hardware-recommendation" class="nav-link">Hardware Recommendations</a></li> -->
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="Palitan ang Wika">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">Filipino</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">English</a>
                        <a href="../zh-cn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="../ja-jp/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                        <a href="../ko-kr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">한국어</a>
                        <a href="../vi-vn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Tiếng Việt</a>
                        <a href="../it-it/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Italiano</a>
                        <a href="../es-es/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Español</a>
                        <a href="../fr-fr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Français</a>
                        <a href="../de-de/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Deutsch</a>
                        <a href="../pt-pt/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Português</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">Filipino</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="Palitan ang Theme">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="Menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Performance Test</a></li>
                <li><a href="#about" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Tungkol sa Tool</a></li>
                <li><a href="#test-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Test Guide</a></li>
                <li><a href="#technology" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Technical Principles</a></li>
                <li><a href="#compatibility" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Compatibility</a></li>
                <li><a href="#gpu-knowledge" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">GPU Knowledge</a></li> -->
                <li><a href="#hardware-recommendation" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Hardware Recommendations</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
          
        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader BM Test</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> Fullscreen
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> Screenshot
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">I-click ang Start Test button para magsimula ng rendering</div>
                                <div class="lg:hidden">I-click ang button sa ibaba para magsimula ng GPU test</div>
                                <div class="text-sm text-gray-300 mt-1">Siguraduhing sumusuporta ang inyong device sa WebGL</div>
                            </div>
                        </div>
                        
                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                
                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">Pumili ng Test Mode</h3>
                                    </div>
                                    
                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Light Test</h4>
                                                        <p class="text-gray-300 text-sm">Basic performance test, angkop para sa low-end devices</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Medium Test</h4>
                                                        <p class="text-gray-300 text-sm">Standard load test, angkop para sa mid-range devices</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Heavy Test</h4>
                                                        <p class="text-gray-300 text-sm">High-intensity test, angkop para sa high-end devices</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Extreme Test</h4>
                                                        <p class="text-gray-300 text-sm">Extreme stress test, gamitin nang may pag-iingat</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            Simulan ang Test
                                        </button>

                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            Itigil ang Test
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                Simulan ang GPU Test
                            </button>
                        </div>
                        
                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>Render Time: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>Triangles: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>Complexity: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>
                        
                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">Nakita ang performance issue, isaalang-alang na babaan ang test level</span>
                        </div>
                    </div>
                    
                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Impormasyon ng Device</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">Tinitingnan...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>WebGL Version:</span>
                                <span id="webgl-version" class="text-right">Tinitingnan...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Browser:</span>
                                <span id="browser-info" class="text-right">Tinitingnan...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Operating System:</span>
                                <span id="os-info" class="text-right">Tinitingnan...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Light Test</h3>
                                    <p>Basic performance test</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Medium Test</h3>
                                    <p>Standard load test</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Heavy Test</h3>
                                    <p>High-intensity test</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Extreme Test</h3>
                                    <p>Extreme stress test</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Custom</h3>
                                    <p>Custom parameter test</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Mga Test Controls</h3>
                        
                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> Simulan ang Test
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> Itigil ang Test
                            </button>
                        </div>

                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>Progreso ng Test</span>
                                <span id="progress-text">0/60 segundo</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bilang ng Triangle</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Kasalukuyan: <span id="triangle-value">50,000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Shader Complexity</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Kasalukuyan: <span id="complexity-value">2.5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tagal ng Test (segundo)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Kasalukuyan: <span id="duration-value">60</span> segundo</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Real-time Performance</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">Score</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">Normal</div>
                                <div class="text-gray-500 dark:text-gray-400">Status ng Temperature</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">Light</div>
                                <div class="text-gray-500 dark:text-gray-400">Test Level</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> I-share ang Mga Resulta
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> Kasaysayan ng Test
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Performance Analysis</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPS Chart</button>
                        <button class="chart-tab" data-chart="score">Score Trend</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Average FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Maximum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Minimum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Katatagan</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Kasaysayan ng Test</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> Linisin
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>Walang test records pa</p>
                        <p class="text-sm">Ang mga natapos na performance tests ay lalabas dito</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Kabuuan <span id="history-count">0</span> tests
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> I-export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Shader BM Introduction Section -->
        <div class="card p-6 mt-6 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                <div class="order-2 md:order-1">
                    <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Tuklasin ang Volume Shader BM</h2>
                    <div class="text-gray-700 dark:text-gray-300 space-y-4">
                        <p>Ang Volume Shader BM ay isang cutting-edge GPU benchmark tool na idinisenyo upang itulak ang inyong graphics hardware sa mga limitasyon nito sa pamamagitan ng complex volumetric shader rendering.</p>
                        <p>Ang aming benchmark ay gumagamit ng advanced 3D volume shaders upang lumikha ng realistic fractal patterns na stress-test sa processing capabilities ng inyong GPU, nagbibigay ng tumpak na performance metrics.</p>
                        <p>Maging kayo ay isang gamer na naghahanap na suriin ang performance ng inyong system, isang propesyonal na kailangan mag-validate ng hardware capabilities, o simpleng curious lang tungkol sa graphical prowess ng inyong device, ang Volume Shader BM ay naghahatid ng tumpak at maaasahang mga resulta.</p>
                        <div class="mt-6">
                            <a href="#test" class="btn-primary inline-flex items-center px-4 py-2 rounded-md">
                                <i class="fas fa-play mr-2"></i> Simulan ang Benchmarking
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-1 md:order-2">
                    <div class="rounded-lg overflow-hidden shadow-lg transform transition-transform hover:scale-105 duration-300">
                        <img src="images/og-image.avif" alt="Volume Shader Benchmark Visualization" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- About Tool Area -->
        <div id="about" class="card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Tungkol sa Volume Shader BM Test</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Ang Volume Shader BM Test ay isang WebGL-based na online GPU performance benchmark tool na sumusukat sa graphics performance ng inyong device sa pamamagitan ng pag-render ng complex 3D volume shaders.</p>
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2">Paglalarawan ng Test Level</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm">Light: 10K triangles</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm">Medium: 50K triangles</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                            <span class="text-sm">Heavy: 200K triangles</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm">Extreme: 500K triangles</span>
                        </div>
                    </div>
                </div>
                <p><strong>Paalala:</strong> Ang mga high-intensity tests ay maaaring magdulot ng lag o pag-init sa mga low-performance devices. Mangyaring pumili ng naaangkop na test level batay sa performance ng inyong device.</p>
            </div>
        </div>

        <!-- Test Guide Area -->
        <div id="test-guide" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-book-open mr-2 text-primary-500"></i>GPU Test Guide
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Preparation -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-clipboard-check mr-2 text-green-500"></i>Paghahanda sa Test
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Isara ang mga hindi kinakailangang programa at browser tabs upang makalaya ang system resources</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Siguraduhing may magandang cooling conditions ang inyong device upang maiwasan ang sobrang pag-init sa panahon ng testing</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Gumamit ng stable na power supply upang maiwasan ang mga interruption sa panahon ng testing</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>I-update ang inyong graphics drivers sa pinakabagong version para sa optimal performance</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Test Steps -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-list-ol mr-2 text-blue-500"></i>Mga Hakbang sa Test
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                <span>Pumili ng naaangkop na test mode (inirerekumenda na magsimula sa Light)</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                <span>I-adjust ang complexity at test duration kung kinakailangan</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                <span>I-click ang Start Test at obserbahan ang real-time performance data</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                                <span>Tingnan ang detalyadong ulat at mga rekumendasyong pagkatapos ng test</span>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- Test Mode Details -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-layer-group mr-2 text-purple-500"></i>Mga Detalye ng Test Mode
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                            <h4 class="font-medium text-green-800 dark:text-green-200">Light Test</h4>
                        </div>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• Angkop para sa office devices at entry-level GPUs</li>
                            <li>• 10K triangles, mababang GPU load</li>
                            <li>• Tagal ng test: 30 segundo</li>
                            <li>• Mababang heat generation, angkop para sa extended runs</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">Medium Test</h4>
                        </div>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Angkop para sa mid-range gaming GPUs</li>
                            <li>• 50K triangles, medium GPU load</li>
                            <li>• Tagal ng test: 60 segundo</li>
                            <li>• Makakakita ng performance bottlenecks at stability</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200">Heavy Test</h4>
                        </div>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• Angkop para sa high-end gaming GPUs</li>
                            <li>• 200K triangles, mataas na GPU load</li>
                            <li>• Tagal ng test: 90 segundo</li>
                            <li>• Sinusubok ang cooling system at power supply stability</li>
                        </ul>
                    </div>

                    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                            <h4 class="font-medium text-red-800 dark:text-red-200">Extreme Test</h4>
                        </div>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• Angkop para sa top-tier professional GPUs</li>
                            <li>• 500K triangles, extreme GPU load</li>
                            <li>• Tagal ng test: 120 segundo</li>
                            <li>• Stress test, maaaring magdulot ng pag-init ng device</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Results Interpretation Guide -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-indigo-500"></i>Gabay sa Pagbabasa ng Mga Resulta
                </h3>
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">Pagsusuri ng FPS</h4>
                            <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                                <li>• <strong>60+ FPS</strong>: Napakahusay na performance</li>
                                <li>• <strong>30-60 FPS</strong>: Magandang performance</li>
                                <li>• <strong>15-30 FPS</strong>: Katamtamang performance</li>
                                <li>• <strong>&lt;15 FPS</strong>: Mababang performance</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">Status ng Temperature</h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• <strong>Normal</strong>: Stable ang GPU temperature</li>
                                <li>• <strong>Warm</strong>: Bahagyang pagtaas ng temperature, normal na range</li>
                                <li>• <strong>Hot</strong>: Kailangan ng pansin sa cooling</li>
                                <li>• <strong>Overheating</strong>: Inirerekumenda na itigil ang test</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">Kabuuang Score</h4>
                            <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                                <li>• <strong>9000+</strong>: Flagship performance</li>
                                <li>• <strong>6000-9000</strong>: High-end performance</li>
                                <li>• <strong>3000-6000</strong>: Mid-range performance</li>
                                <li>• <strong>&lt;3000</strong>: Entry-level performance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Important Notes -->
            <div class="mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Mahahalagang Paalala</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Bigyang pansin ang battery level at init kapag nagpapatakbo ng high-intensity tests sa mobile devices</li>
                                <li>• Kung may mga screen artifacts o system freezes na mangyayari sa panahon ng testing, itigil kaagad ang test</li>
                                <li>• Ang mga test results ay para sa reference lamang; ang tunay na gaming performance ay naapektuhan din ng CPU, memory, at iba pang mga salik</li>
                                <li>• Ang regular na testing ay makakatulong sa pagsubaybay sa mga pagbabago sa GPU performance at maagahan na matukoy ang mga hardware issues</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Principles Area -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Mga Prinsipyong Teknikal</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Ang tool na ito ay gumagamit ng advanced WebGL 2.0 technology at volume shader algorithms upang maipatupad ang GPU performance testing sa pamamagitan ng mga sumusunod na pamamaraan:</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>Volume Rendering Technology</strong>: Gumagamit ng mga complex mathematical functions upang makabuo ng 3D volume textures, ginagaya ang hugis at texture ng mga toxic mushrooms</li>
                    <li><strong>Dynamic Shaders</strong>: Dynamic na nag-aadjust ng fragment shader complexity batay sa test level, pinapataas ang GPU computational load</li>
                    <li><strong>Real-time Performance Monitoring</strong>: Sinusubaybayan ang mga pangunahing metrics tulad ng FPS at render time sa pamamagitan ng WebGL extensions at performance API</li>
                    <li><strong>Temperature Inference Algorithm</strong>: Hinuhulaan ang device temperature status sa pamamagitan ng performance degradation curves</li>
                </ul>
                <p>Ang test algorithm ay unti-unting pinapataas ang rendering complexity hanggang maabot nito ang performance limit ng device, tumpak na sinusukat ang processing capability ng GPU.</p>
            </div>
        </div>

        <!-- Compatibility Area -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Compatibility ng Browser</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Lubos na suportado. Pinakabagong version ay inirerekumenda para sa pinakamahusay na performance.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Lubos na suportado na may napakahusay na performance.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Basic na suporta, ang ilang advanced features ay maaaring limitado.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Lubos na suportado, performance ay katulad ng Chrome.</p>
                </div>
            </div>
        </div>

        <!-- GPU Knowledge Base Area -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>GPU Knowledge Base
            </h2>
            
            <!-- GPU Basic Knowledge -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>GPU Basics
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">What is a GPU</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">Graphics Processing Unit, a chip specialized for processing graphics rendering and parallel computing, with thousands of small cores that excel at handling many simple tasks simultaneously.</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPU vs CPU</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPUs have fewer powerful cores, suitable for complex logical operations; GPUs have many simple cores, ideal for parallel computing and graphics rendering. They work together for optimal performance.</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">GPU Types</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">Integrated GPUs: Built into the CPU, low power consumption but limited performance; Dedicated GPUs: Separate GPU chips, powerful performance, suitable for gaming and professional work.</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Parameters Analysis -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>Key Parameters Analysis
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Video Memory (VRAM)</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Dedicated memory for storing graphics data</li>
                                <li>• Capacity affects high-resolution gaming performance</li>
                                <li>• 8GB+ recommended for mainstream games</li>
                                <li>• 12GB+ needed for 4K gaming or professional work</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Core Clock</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Base clock: GPU's stable operating frequency</li>
                                <li>• Boost clock: Frequency after automatic overclocking</li>
                                <li>• Higher frequency means stronger computing power</li>
                                <li>• Can be further improved through overclocking</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Stream Processors</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Core units that execute parallel computing</li>
                                <li>• More units represent stronger parallel processing capability</li>
                                <li>• Called CUDA cores by NVIDIA</li>
                                <li>• Called Stream Processors (SP) by AMD</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Bus Width & Bandwidth</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Memory bus width affects data transfer</li>
                                <li>• 256-bit and above is high-end configuration</li>
                                <li>• Bandwidth = Bus width × Memory frequency</li>
                                <li>• High bandwidth reduces performance bottlenecks</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU Architecture Evolution -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>GPU Architecture Evolution
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>NVIDIA Architecture
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40 Series</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30 Series</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20 Series</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10 Series</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>AMD Architecture
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000 Series</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000 Series</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000 Series</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500 Series</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Factors Affecting GPU Performance -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>Factors Affecting GPU Performance
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Temperature Control</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Overheating causes throttling, affecting performance</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Power Supply</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Insufficient power limits GPU performance</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Drivers</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Latest drivers optimize game performance</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">System Configuration</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPU and RAM also affect overall performance</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>GPU Recommendation Guide
            </h2>
            
            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>Budget-Based Recommendations
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">Entry Level</h4>
                            <!-- <p class="text-sm text-green-600 dark:text-green-400">￥1000-2000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1080p high quality gaming</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Recommended ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Best value choice</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Recommended ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>Suitable for:</strong> 1080p gaming, light content creation, daily office work</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">Mid-Range</h4>
                            <!-- <p class="text-sm text-blue-600 dark:text-blue-400">￥2000-4000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1440p high quality</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Recommended ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Large VRAM advantage</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Recommended ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>Suitable for:</strong> 1440p gaming, video editing, live streaming</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">High-End</h4>
                            <!-- <p class="text-sm text-purple-600 dark:text-purple-400">￥4000-8000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">4K gaming powerhouse</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Recommended ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">24GB large VRAM</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Recommended ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>Suitable for:</strong> 4K gaming, professional rendering, AI computing</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">Flagship</h4>
                            <!-- <p class="text-sm text-orange-600 dark:text-orange-400">￥8000+</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Ultimate performance</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Recommended ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080 Super</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Value flagship</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Recommended ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080-super">
                                     <i class="fas fa-shopping-cart mr-1"></i>View Purchase
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>Suitable for:</strong> 8K gaming, workstations, enthusiasts</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Usage-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-tasks mr-2 text-blue-500"></i>Usage-Based Recommendations
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Gaming GPUs -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                            <h4 class="font-semibold text-red-800 dark:text-red-200">Gaming GPUs</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Esports Gaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060, RX 7600</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">High FPS 1080p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">AAA Titles</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7700 XT</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">1440p high quality</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">4K Gaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">4K Ultra settings</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Content Creation -->
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">Content Creation</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Video Editing</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7900 GRE</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Hardware encoding acceleration</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">3D Rendering</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">CUDA/OptiX acceleration</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Live Streaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060 and above</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">NVENC encoding</div>
                                 <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                     <i class="fas fa-shopping-cart mr-1"></i>View RTX 4060 Series
                                 </a>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Professional Work -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200">Professional Work</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">AI Development</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Large VRAM requirement</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">CAD Design</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, Quadro series</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Professional driver optimization</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                         <i class="fas fa-shopping-cart mr-1"></i>Quadro Series
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Scientific Computing</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Double precision floating point</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                         <i class="fas fa-shopping-cart mr-1"></i>A5000
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
            </div>
            
            <!-- Purchase Advice -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>Purchase Advice
                </h3>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                                Important Considerations
                            </h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Ensure sufficient power supply wattage</li>
                                <li>• Check case space and cooling</li>
                                <li>• Consider CPU performance matching</li>
                                <li>• Pay attention to VRAM capacity requirements</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                                <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                                Best Time to Buy
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• 3-6 months after new product release</li>
                                <li>• Black Friday and other shopping events</li>
                                <li>• After crypto mining downturns</li>
                                <li>• Rational observation of the used market</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                                Brand Selection
                            </h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                                <li>• ASUS ROG: Top choice for high-end gamers</li>
                                <li>• MSI Gaming: Balanced price-performance ratio</li>
                                <li>• GIGABYTE AORUS: Excellent cooling</li>
                                <li>• Galax: Good value for entry-level</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Frequently Asked Questions</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Why does my device heat up during testing?</h3>
                    <p class="text-gray-700 dark:text-gray-300">This is normal. The Toxic Mushroom test pushes your GPU to work at full capacity, so heat generation is unavoidable. If the temperature gets too high, we recommend stopping the test or lowering the test level.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">What does the test result score represent?</h3>
                    <p class="text-gray-700 dark:text-gray-300">The score takes into account FPS, rendering stability, and test level. A higher score indicates stronger GPU performance and can be used for side-by-side comparison with other devices.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">What if my browser shows WebGL is not supported?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Please make sure your browser is up to date, check if hardware acceleration is enabled, or try a different browser. Some mobile devices may not support WebGL 2.0.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Will my test data be uploaded?</h3>
                    <p class="text-gray-700 dark:text-gray-300">All test data is stored locally and will not be uploaded to any server. You can safely perform tests without worrying about privacy issues.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">Propesyonal na Online GPU Performance Testing Tool</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">Volume Shader BM Test</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Mga Kaugnay na Link</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Gabay ng User</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Technical Support</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Feedback</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Disclaimer</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Ang tool na ito ay para sa GPU performance testing lamang. Ang pag-init ng device at power consumption sa panahon ng paggamit ay normal na phenomena. Mangyaring pumili ng naaangkop na test level batay sa mga kakayahan ng inyong device.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="static/js/app.js"></script>
    
    <!-- Script para sa pagpapalit ng wika -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');

            // Ipakita/itago ang language dropdown menu
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });

            // Isara ang dropdown menu kapag nag-click sa ibang parte ng page
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>