<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>毒蘑菇显卡测试 - 极致GPU性能挑战平台 | 专业在线GPU性能基准测试工具</title>
    <meta name="description" content="毒蘑菇显卡测试 - 全网最强GPU性能挑战平台！采用前沿WebGL 2.0技术，打造沉浸式3D体积渲染测试体验。一键检测显卡真实实力，多维度性能分析，让您的GPU潜能无处遁形！">
    <meta name="keywords" content="毒蘑菇显卡测试,GPU跑分神器,显卡性能挑战,WebGL 2.0测试,3D体积渲染,显卡压力测试,GPU基准测试,在线显卡评测,显卡跑分排行,GPU性能检测">
    <meta name="author" content="毒蘑菇显卡测试 - 专业GPU性能评测团队">
    <link rel="canonical" href="https://volumeshaderbmtest.com/zh-cn/">
    <link rel="icon" href="logo.png" type="image/png">
    <link rel="apple-touch-icon" href="logo.png">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/zh-cn/">
    <meta property="og:title" content="毒蘑菇显卡测试 | 极致GPU性能挑战平台 - 专业在线GPU性能基准测试工具">
    <meta property="og:description" content="全网最强GPU性能挑战平台！采用前沿WebGL 2.0技术，打造沉浸式3D体积渲染测试体验。一键检测显卡真实实力，让您的GPU潜能无处遁形！">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.avif">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/zh-cn/">
    <meta property="twitter:title" content="毒蘑菇显卡测试 | 极致GPU性能挑战平台 - 专业在线GPU性能基准测试工具">
    <meta property="twitter:description" content="全网最强GPU性能挑战平台！采用前沿WebGL 2.0技术，打造沉浸式3D体积渲染测试体验。一键检测显卡真实实力，让您的GPU潜能无处遁形！">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/twitter-image.jpg">
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************" crossorigin="anonymous"></script>
    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#website",
                "url": "https://volumeshaderbmtest.com/zh-cn/",
                "name": "毒蘑菇显卡测试",
                "description": "革命性GPU性能挑战平台，采用尖端WebGL 2.0技术打造极致3D体积渲染测试体验",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/zh-cn/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/zh-cn/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#organization",
                "name": "毒蘑菇显卡测试 - 专业GPU性能评测团队",
                "url": "https://volumeshaderbmtest.com/zh-cn/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/zh-cn/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#software",
                "name": "毒蘑菇显卡测试",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/zh-cn/",
                "description": "颠覆性GPU性能挑战神器！基于前沿WebGL 2.0技术，打造沉浸式3D体积渲染测试体验。支持轻度、中度、重度、极限四种挑战模式，实时监控FPS、温度等关键性能指标，让显卡实力一览无余。",
                "featureList": [
                    "GPU性能测试",
                    "实时FPS监控",
                    "温度状态监控",
                    "性能历史记录",
                    "测试结果分析",
                    "WebGL体积渲染",
                    "多难度测试模式"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "CNY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#gpu-knowledge",
                "headline": "GPU显卡知识库 - 完整的显卡技术指南",
                "description": "详细介绍GPU基础知识、关键参数、架构发展和性能影响因素，为显卡选购和性能优化提供专业指导。",
                "author": {
                    "@type": "Organization",
                    "name": "毒蘑菇测试团队"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/zh-cn/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/zh-cn/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "为什么我的设备测试时会发热？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "这是正常现象。毒蘑菇测试会让GPU全力工作，产生热量是不可避免的。如果温度过高，建议停止测试或降低测试等级。"
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "测试结果的分数代表什么？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "分数综合考虑了FPS、渲染稳定性和测试等级。分数越高代表GPU性能越强，可以与其他设备进行横向对比。"
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "浏览器显示WebGL不支持怎么办？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "请确认浏览器是最新版本，检查硬件加速是否开启，或尝试更换浏览器。部分移动设备可能不支持WebGL 2.0。"
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "测试数据会被上传吗？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "所有测试数据都保存在本地，不会上传到任何服务器。您可以安全地进行测试，无需担心隐私问题。"
                        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#gpu-recommendations",
                "name": "显卡推荐指南",
                "description": "按预算和用途分类的专业显卡推荐",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
                        "description": "入门级游戏显卡，适合1080p高画质游戏",
                        "category": "显卡",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1500",
                            "highPrice": "2000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
                        "description": "性价比之选，入门级显卡推荐",
                        "category": "显卡",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1400",
                            "highPrice": "1800",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
                        "description": "中端游戏显卡，支持1440p高画质游戏",
                        "category": "显卡",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "3000",
                            "highPrice": "3500",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 4,
                        "name": "NVIDIA GeForce RTX 4090",
                        "description": "旗舰级显卡，极致4K游戏性能",
                        "category": "显卡",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "12000",
                            "highPrice": "15000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/zh-cn/#test-guide",
                "name": "GPU性能测试指南",
                "description": "详细的GPU性能测试步骤和最佳实践",
                "image": "https://volumeshaderbmtest.com/zh-cn/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "支持WebGL的浏览器"
                    },
                    {
                        "@type": "HowToSupply", 
                        "name": "独立显卡或集成显卡"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "毒蘑菇显卡测试工具"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "测试前准备",
                        "text": "关闭不必要的程序和浏览器标签，确保设备有良好的散热环境"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "选择测试模式",
                        "text": "根据设备性能选择合适的测试模式，建议从轻度开始"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "开始测试",
                        "text": "点击开始测试，观察实时性能数据变化"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "查看结果",
                        "text": "测试完成后查看详细报告和性能建议"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="static/js/js.js"></script>
    <script src="static/js/chart.js"></script>
    <script src="static/js/three.min.js"></script>
    <link rel="stylesheet" href="static/css/all.min.css">
    <link href="static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- 头部导航栏 -->
    <header>
        <div class="container flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cube text-2xl text-gradient bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent"></i>
                <h1 class="text-xl font-semibold">毒蘑菇显卡测试</h1>
            </div>
            <nav class="nav-menu md:block">
                <ul>
                    <li><a href="#test" class="active">🚀 极限挑战</a></li>
                    <li><a href="#about">💎 工具介绍</a></li>
                    <li><a href="#test-guide">📖 挑战指南</a></li>
                    <li><a href="#technology">⚡ 核心技术</a></li>
                    <li><a href="#compatibility">🌐 兼容性</a></li>
                    <li><a href="#gpu-knowledge">🧠 GPU百科</a></li>
                    <!-- <li><a href="#hardware-recommendation">🎯 装机推荐</a></li> -->
                    <li><a href="#faq">❓ 疑难解答</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="切换语言">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">中文</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">English</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">中文</a>
                        <a href="../ja-jp/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                        <a href="../ko-kr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">한국어</a>
                        <a href="../vi-vn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Tiếng Việt</a>
                        <a href="../it-it/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Italiano</a>
                        <a href="../es-es/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Español</a>
                        <a href="../fr-fr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Français</a>
                        <a href="../de-de/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Deutsch</a>
                        <a href="../pt-pt/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Português</a>
                    </div>
                </div>
                <div class="theme-toggle" id="theme-toggle" title="切换主题">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden" id="mobile-menu-btn" title="菜单">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>
    
    <!-- 主要内容区域 -->
    <div class="container">
          
        <!-- 主要测试区域 -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D渲染区域 -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">🍄 毒蘑菇显卡测试</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> 全屏
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> 截图
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL渲染画布 -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- 渲染状态覆盖层 -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">点击开始测试按钮启动渲染</div>
                                <div class="lg:hidden">点击下方按钮开始GPU测试</div>
                                <div class="text-sm text-gray-300 mt-1">确保您的设备支持WebGL</div>
                            </div>
                        </div>
                        
                        <!-- 移动端测试控制覆盖层 -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- 关闭按钮 -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                
                                <!-- 内容区域 -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- 标题 -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">选择测试模式</h3>
                                    </div>
                                    
                                    <!-- 测试模式选择 - 可滚动区域 -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">轻度测试</h4>
                                                        <p class="text-gray-300 text-sm">基础性能测试，适合低端设备</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">中度测试</h4>
                                                        <p class="text-gray-300 text-sm">标准负载测试，适合中端设备</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">重度测试</h4>
                                                        <p class="text-gray-300 text-sm">高强度测试，适合高端设备</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">极限测试</h4>
                                                        <p class="text-gray-300 text-sm">极限压力测试，谨慎使用</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 测试控制按钮 - 固定在底部 -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            开始测试
                                        </button>
                                        
                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            停止测试
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 移动端快速开始按钮 -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                开始GPU测试
                            </button>
                        </div>
                        
                        <!-- FPS和性能信息覆盖层 -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>渲染时间: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>三角面: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>复杂度: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>
                        
                        <!-- 警告信息 -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">检测到性能问题，建议降低测试等级</span>
                        </div>
                    </div>
                    
                    <!-- 设备信息 -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">设备信息</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">检测中...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>WebGL版本:</span>
                                <span id="webgl-version" class="text-right">检测中...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>浏览器:</span>
                                <span id="browser-info" class="text-right">检测中...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>操作系统:</span>
                                <span id="os-info" class="text-right">检测中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 控制面板和结果区域 -->
                <div class="space-y-4 hidden lg:block">
                    <!-- 测试模式选择区域 -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>🌱 轻度挑战</h3>
                                    <p>温和启动，探索GPU潜力</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>⚡ 中度挑战</h3>
                                    <p>标准战场，考验真实实力</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>🔥 重度挑战</h3>
                                    <p>烈火考验，突破性能极限</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>💀 极限挑战</h3>
                                    <p>死神降临，终极毁灭测试</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>自定义</h3>
                                    <p>自定义参数测试</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- 测试控制 -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">测试控制</h3>
                        
                        <!-- 测试按钮 -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> 开始测试
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> 停止测试
                            </button>
                        </div>
                        
                        <!-- 测试进度 -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>测试进度</span>
                                <span id="progress-text">0/60秒</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- 自定义参数 -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">三角面数量</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">当前: <span id="triangle-value">50,000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">着色器复杂度</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">当前: <span id="complexity-value">2.5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">测试时长 (秒)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">当前: <span id="duration-value">60</span> 秒</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 当前性能指标 -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">实时性能</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">分数</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">正常</div>
                                <div class="text-gray-500 dark:text-gray-400">温度状态</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">轻度</div>
                                <div class="text-gray-500 dark:text-gray-400">测试等级</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 快速操作 -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> 分享结果
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> 测试历史
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 性能图表和测试历史 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 性能图表区域 -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">性能分析</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPS图表</button>
                        <button class="chart-tab" data-chart="score">分数趋势</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">平均FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">最高FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">最低FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">稳定性</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- 测试历史区域 -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">测试历史</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> 清除
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>暂无测试记录</p>
                        <p class="text-sm">完成的性能测试将显示在这里</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- 历史记录项将通过JavaScript动态添加 -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        共 <span id="history-count">0</span> 次测试
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> 导出
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关于工具区域 -->
        <div id="about" class="card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">💎 关于毒蘑菇显卡测试</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>🍄 毒蘑菇显卡测试 - 革命性的GPU性能挑战神器！采用尖端WebGL 2.0技术，通过极致3D体积渲染技术，为您的显卡带来前所未有的终极挑战体验。</p>
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2">🎯 挑战等级说明</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm">🌱 轻度: 1万三角面</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm">⚡ 中度: 5万三角面</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                            <span class="text-sm">🔥 重度: 20万三角面</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm">💀 极限: 50万三角面</span>
                        </div>
                    </div>
                </div>
                <p><strong>⚠️ 挑战警告：</strong>高强度挑战可能让您的显卡咆哮不已！请根据设备实力选择合适的挑战等级，享受极限性能的快感。</p>
            </div>
        </div>

        <!-- 测试指南区域 -->
        <div id="test-guide" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-book-open mr-2 text-primary-500"></i>📖 GPU挑战指南
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 测试前准备 -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-clipboard-check mr-2 text-green-500"></i>测试前准备
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>关闭不必要的程序和浏览器标签，释放系统资源</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>确保设备有良好的散热环境，避免过热影响测试</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>使用稳定的电源供应，避免测试过程中断电</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>更新显卡驱动到最新版本以获得最佳性能</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- 测试步骤 -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-list-ol mr-2 text-blue-500"></i>测试步骤
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                <span>选择合适的测试模式（建议从轻度开始）</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                <span>根据需要调整复杂度和测试时长</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                <span>点击开始测试，观察实时性能数据</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                                <span>测试完成后查看详细报告和建议</span>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- 测试模式详解 -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-layer-group mr-2 text-purple-500"></i>测试模式详解
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                            <h4 class="font-medium text-green-800 dark:text-green-200">🌱 轻度挑战</h4>
                        </div>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• 新手友好，温和唤醒GPU潜能</li>
                            <li>• 1万三角面，轻松热身模式</li>
                            <li>• 挑战时间：30秒</li>
                            <li>• 低温运行，探索基础性能边界</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">⚡ 中度挑战</h4>
                        </div>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• 标准战场，考验真实游戏实力</li>
                            <li>• 5万三角面，雷电般的渲染冲击</li>
                            <li>• 挑战时间：60秒</li>
                            <li>• 揭露隐藏瓶颈，测试稳定极限</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200">🔥 重度挑战</h4>
                        </div>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• 烈火炼金，高端显卡的试炼场</li>
                            <li>• 20万三角面，熔岩般的渲染风暴</li>
                            <li>• 挑战时间：90秒</li>
                            <li>• 烤验散热极限，挑战供电稳定</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                            <h4 class="font-medium text-red-800 dark:text-red-200">💀 极限挑战</h4>
                        </div>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• 死神降临，顶级显卡的终极审判</li>
                            <li>• 50万三角面，毁灭性渲染地狱</li>
                            <li>• 挑战时间：120秒</li>
                            <li>• 极限压榨，显卡咆哮的终极试炼</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 结果解读指南 -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-indigo-500"></i>结果解读指南
                </h3>
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">FPS分析</h4>
                            <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                                <li>• <strong>60+ FPS</strong>: 性能优秀</li>
                                <li>• <strong>30-60 FPS</strong>: 性能良好</li>
                                <li>• <strong>15-30 FPS</strong>: 性能一般</li>
                                <li>• <strong>&lt;15 FPS</strong>: 性能偏低</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">温度状态</h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• <strong>正常</strong>: GPU温度稳定</li>
                                <li>• <strong>温热</strong>: 轻微升温，正常范围</li>
                                <li>• <strong>较热</strong>: 需要注意散热</li>
                                <li>• <strong>过热</strong>: 建议停止测试</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">综合评分</h4>
                            <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                                <li>• <strong>9000+</strong>: 旗舰级性能</li>
                                <li>• <strong>6000-9000</strong>: 高端性能</li>
                                <li>• <strong>3000-6000</strong>: 中端性能</li>
                                <li>• <strong>&lt;3000</strong>: 入门级性能</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 注意事项 -->
            <div class="mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">重要提醒</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• 移动设备进行高强度测试时请注意电量和发热情况</li>
                                <li>• 如果测试过程中出现花屏、死机等异常，请立即停止测试</li>
                                <li>• 测试结果仅供参考，实际游戏性能还受到CPU、内存等因素影响</li>
                                <li>• 定期测试可以监控显卡性能变化，及时发现硬件问题</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术原理区域 -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">⚡ 核心技术</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>毒蘑菇显卡测试采用革命性的WebGL 2.0技术和前沿体积着色器算法，打造极致GPU性能挑战体验：</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>🍄 毒蘑菇体积渲染</strong>：运用尖端数学函数构建3D体积纹理，打造震撼视觉的毒蘑菇形态</li>
                    <li><strong>🔥 智能动态着色器</strong>：实时调节fragment shader复杂度，精准控制GPU负载强度</li>
                    <li><strong>📊 极速性能监控</strong>：借助WebGL扩展和performance API，毫秒级监控关键性能指标</li>
                    <li><strong>🌡️ 温度智能推断</strong>：通过性能曲线变化，精准预测设备温度状态</li>
                </ul>
                <p>测试算法会逐渐增加渲染复杂度，直到达到设备性能极限，从而准确评估GPU的处理能力。</p>
            </div>
        </div>

        <!-- 兼容性区域 -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">浏览器兼容性</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">完全支持，推荐使用最新版本以获得最佳性能。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">完全支持，性能表现优秀。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">基本支持，部分高级特性可能受限。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">完全支持，与Chrome性能相近。</p>
                </div>
            </div>
        </div>

        <!-- GPU知识库区域 -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>GPU知识库
            </h2>
            
            <!-- GPU基础知识 -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>GPU基础知识
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">什么是GPU</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">图形处理器(Graphics Processing Unit)，专门用于处理图形渲染和并行计算的芯片，拥有数千个小核心，擅长同时处理大量简单任务。</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPU vs CPU</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPU拥有少量强大核心，适合复杂逻辑运算；GPU拥有大量简单核心，适合并行计算和图形渲染，两者协同工作发挥最佳性能。</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">显卡分类</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">集成显卡：集成在CPU内，功耗低但性能有限；独立显卡：独立GPU芯片，性能强大，适合游戏和专业工作。</p>
                    </div>
                </div>
            </div>
            
            <!-- 关键参数解析 -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>关键参数解析
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">显存(VRAM)</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 存储图形数据的专用内存</li>
                                <li>• 容量影响高分辨率游戏表现</li>
                                <li>• 主流游戏推荐8GB以上</li>
                                <li>• 4K游戏或专业工作需要12GB+</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">核心频率</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 基础频率：GPU稳定运行频率</li>
                                <li>• 加速频率：自动超频后的频率</li>
                                <li>• 频率越高，计算能力越强</li>
                                <li>• 可通过超频进一步提升</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">流处理器</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 执行并行计算的核心单元</li>
                                <li>• 数量多代表并行能力强</li>
                                <li>• NVIDIA称为CUDA核心</li>
                                <li>• AMD称为流处理器(SP)</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">位宽带宽</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 内存总线位宽影响数据传输</li>
                                <li>• 256位以上为高端配置</li>
                                <li>• 带宽 = 位宽 × 内存频率</li>
                                <li>• 高带宽减少性能瓶颈</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU架构发展 -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>GPU架构发展
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>NVIDIA架构
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40系</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30系</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20系</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10系</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>AMD架构
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000系</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000系</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000系</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500系</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 性能影响因素 -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>影响GPU性能的因素
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">温度控制</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">过热会导致降频，影响性能表现</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">电源供应</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">功率不足会限制GPU性能发挥</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">驱动程序</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">最新驱动能优化游戏性能</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">系统配置</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPU和内存也会影响整体表现</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 硬件推荐区域 -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>显卡推荐指南
            </h2>
            
            <!-- 预算分档推荐 -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>预算分档推荐
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- 入门级 -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">入门级</h4>
                            <!-- <p class="text-sm text-green-600 dark:text-green-400">￥1000-2000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1080p高画质游戏</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">推荐 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">性价比之选</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">推荐 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>适合：</strong>1080p游戏、轻度内容创作、日常办公</p>
                        </div>
                    </div>
                    
                    <!-- 中端级 -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">中端级</h4>
                            <!-- <p class="text-sm text-blue-600 dark:text-blue-400">￥2000-4000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1440p高画质</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">推荐 ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">大显存优势</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">推荐 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>适合：</strong>1440p游戏、视频剪辑、直播串流</p>
                        </div>
                    </div>
                    
                    <!-- 高端级 -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">高端级</h4>
                            <!-- <p class="text-sm text-purple-600 dark:text-purple-400">￥4000-8000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">4K游戏利器</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">推荐 ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">24GB大显存</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">推荐 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>适合：</strong>4K游戏、专业渲染、AI计算</p>
                        </div>
                    </div>
                    
                    <!-- 旗舰级 -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">旗舰级</h4>
                            <!-- <p class="text-sm text-orange-600 dark:text-orange-400">￥8000+</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">极致性能</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">推荐 ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080 Super</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">性价比旗舰</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">推荐 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080-super">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看购买
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>适合：</strong>8K游戏、工作站、发烧友</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 用途分类推荐 -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-tasks mr-2 text-blue-500"></i>用途分类推荐
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 游戏显卡 -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                            <h4 class="font-semibold text-red-800 dark:text-red-200">游戏显卡</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">电竞游戏</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060、RX 7600</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">高帧率1080p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">3A大作</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070、RX 7700 XT</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">1440p高画质</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">4K游戏</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080、RTX 4090</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">4K Ultra设置</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                    
                    <!-- 内容创作 -->
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">内容创作</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">视频剪辑</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070、RX 7900 GRE</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">硬件编码加速</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">3D渲染</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080、RTX 4090</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">CUDA/OptiX加速</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">直播串流</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060以上</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">NVENC编码</div>
                                 <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                     <i class="fas fa-shopping-cart mr-1"></i>查看RTX 4060系列
                                 </a>
                             </div>
                         </div>
                    </div>
                    
                    <!-- 专业工作 -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200">专业工作</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">AI开发</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090、RTX 4080</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">大显存需求</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">CAD设计</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070、Quadro系列</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">专业驱动优化</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                         <i class="fas fa-shopping-cart mr-1"></i>Quadro系列
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">科学计算</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090、A5000</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">双精度浮点</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                         <i class="fas fa-shopping-cart mr-1"></i>A5000
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
            </div>
            
            <!-- 购买建议 -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>购买建议
                </h3>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                                注意事项
                            </h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• 确认电源功率是否充足</li>
                                <li>• 检查机箱空间和散热</li>
                                <li>• 考虑CPU性能匹配</li>
                                <li>• 关注显存容量需求</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                                <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                                购买时机
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• 新品发布后3-6个月</li>
                                <li>• 黑五等购物节</li>
                                <li>• 挖矿退潮时期</li>
                                <li>• 二手市场理性观察</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                                品牌选择
                            </h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                                <li>• 华硕ROG：高端玩家首选</li>
                                <li>• 微星Gaming：性价比均衡</li>
                                <li>• 技嘉AORUS：散热优秀</li>
                                <li>• 影驰：入门级性价比</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题区域 -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">常见问题</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">为什么我的设备测试时会发热？</h3>
                    <p class="text-gray-700 dark:text-gray-300">这是正常现象。毒蘑菇测试会让GPU全力工作，产生热量是不可避免的。如果温度过高，建议停止测试或降低测试等级。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">测试结果的分数代表什么？</h3>
                    <p class="text-gray-700 dark:text-gray-300">分数综合考虑了FPS、渲染稳定性和测试等级。分数越高代表GPU性能越强，可以与其他设备进行横向对比。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">浏览器显示WebGL不支持怎么办？</h3>
                    <p class="text-gray-700 dark:text-gray-300">请确认浏览器是最新版本，检查硬件加速是否开启，或尝试更换浏览器。部分移动设备可能不支持WebGL 2.0。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">测试数据会被上传吗？</h3>
                    <p class="text-gray-700 dark:text-gray-300">所有测试数据都保存在本地，不会上传到任何服务器。您可以安全地进行测试，无需担心隐私问题。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">毒蘑菇显卡测试</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">专业的在线GPU性能测试工具</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">毒蘑菇显卡测试</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">相关链接</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">使用说明</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">技术支持</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">问题反馈</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">免责声明</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">本工具仅用于GPU性能测试，使用过程中产生的设备发热、耗电等情况属于正常现象。请根据设备性能合理选择测试等级。</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="static/js/app.js"></script>
    
    <!-- 语言切换脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            // 切换语言下拉菜单显示/隐藏
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });
            
            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>