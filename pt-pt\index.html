<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM Test Website - Ferramenta Online de Avaliação de Performance GPU</title>
    <meta name="description" content="Ferramenta profissional online de teste de performance GPU que testa o desempenho da placa gráfica através de renderização complexa de volume shader 3D, com suporte para monitoramento FPS em tempo real e análise de performance.">
    <meta name="keywords" content="teste de placa gráfica,teste GPU,Toxic Mushroom Test,volumeshader,WebGL,teste FPS">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/pt-pt/">
    <link rel="icon" href="../logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="../logo.svg">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/pt-pt/">
    <meta property="og:title" content="Volume Shader BM Test | Ferramenta Online de Avaliação de Performance GPU">
    <meta property="og:description" content="Ferramenta profissional online de teste de performance GPU que testa o desempenho da placa gráfica através de renderização complexa de volume shader 3D, com suporte para monitoramento FPS em tempo real e análise de performance.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.avif">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/pt-pt/">
    <meta property="twitter:title" content="Volume Shader BM Test | Ferramenta Online de Avaliação de Performance GPU">
    <meta property="twitter:description" content="Ferramenta profissional online de teste de performance GPU que testa o desempenho da placa gráfica através de renderização complexa de volume shader 3D, com suporte para monitoramento FPS em tempo real e análise de performance.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/og-img.avif">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/#website",
                "url": "https://volumeshaderbmtest.com/pt-pt/",
                "name": "Volume Shader BM Test",
                "description": "Ferramenta Profissional Online de Avaliação de Performance GPU",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/#organization",
                "name": "Toxic Mushroom Test Team",
                "url": "https://volumeshaderbmtest.com/pt-pt/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/#software",
                "name": "Volume Shader BM Test",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/pt-pt/",
                "description": "Ferramenta online de avaliação de performance GPU baseada em WebGL que suporta quatro modos de teste: leve, médio, pesado e extremo, com monitoramento em tempo real de FPS, temperatura e outras métricas importantes de performance.",
                "featureList": [
                    "Teste de Performance GPU",
                    "Monitoramento FPS em Tempo Real",
                    "Monitoramento de Status de Temperatura",
                    "Gravação de Histórico de Performance",
                    "Análise de Resultados de Teste",
                    "Renderização de Volume WebGL",
                    "Múltiplos Modos de Teste de Dificuldade"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "CNY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/#gpu-knowledge",
                "headline": "Base de Conhecimento GPU - Guia Técnico Completo para Placas Gráficas",
                "description": "Introdução detalhada aos fundamentos de GPU, parâmetros-chave, desenvolvimento de arquitetura e fatores que afetam a performance, fornecendo orientação profissional para seleção de placas gráficas e otimização de performance.",
                "author": {
                    "@type": "Organization",
                    "name": "Toxic Mushroom Test Team"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "Por que meu dispositivo aquece durante o teste?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Isso é normal. O Toxic Mushroom Test faz a GPU trabalhar com capacidade total, e a geração de calor é inevitável. Se a temperatura estiver muito alta, é recomendado parar o teste ou reduzir o nível de teste."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "O que significa a pontuação nos resultados do teste?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "A pontuação considera FPS, estabilidade de renderização e nível de teste. Uma pontuação mais alta representa performance GPU mais forte e pode ser comparada horizontalmente com outros dispositivos."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "O que devo fazer se meu navegador mostrar que WebGL não é suportado?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Por favor, certifique-se de que seu navegador está atualizado, verifique se a aceleração de hardware está habilitada, ou tente trocar de navegador. Alguns dispositivos móveis podem não suportar WebGL 2.0."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Os dados do teste são enviados para algum lugar?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Todos os dados do teste são armazenados localmente e não são enviados para um servidor. Você pode realizar testes com segurança sem se preocupar com questões de privacidade."
                        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/#gpu-recommendations",
                "name": "Guia de Recomendações de Placas Gráficas",
                "description": "Recomendações profissionais de placas gráficas categorizadas por orçamento e propósito",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
                        "description": "Placa gráfica gaming de entrada, adequada para gaming 1080p de alta qualidade",
                        "category": "Placa Gráfica",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "EUR",
                            "lowPrice": "300",
                            "highPrice": "400",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
                        "description": "Melhor escolha custo-benefício, placa gráfica de entrada recomendada",
                        "category": "Placa Gráfica",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "EUR",
                            "lowPrice": "280",
                            "highPrice": "360",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
                        "description": "Placa gráfica gaming de gama média, suporta gaming 1440p de alta qualidade",
                        "category": "Placa Gráfica",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "EUR",
                            "lowPrice": "600",
                            "highPrice": "700",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 4,
                        "name": "NVIDIA GeForce RTX 4090",
                        "description": "Placa gráfica topo de linha, performance ultimate para gaming 4K",
                        "category": "Placa Gráfica",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "EUR",
                            "lowPrice": "1600",
                            "highPrice": "2000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/#test-guide",
                "name": "Guia de Teste de Performance GPU",
                "description": "Passos detalhados de teste de performance GPU e melhores práticas",
                "image": "https://volumeshaderbmtest.com/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "Navegador compatível com WebGL"
                    },
                    {
                        "@type": "HowToSupply",
                        "name": "Placa gráfica dedicada ou integrada"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "Ferramenta de Teste Volume Shader BM"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "Preparação antes do teste",
                        "text": "Feche programas desnecessários e abas do navegador, garanta um bom ambiente de dissipação de calor do dispositivo"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "Selecionar modo de teste",
                        "text": "Escolha um modo de teste adequado de acordo com a performance do dispositivo, recomenda-se começar com o modo leve"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "Iniciar teste",
                        "text": "Clique em iniciar teste e observe as mudanças dos dados de performance em tempo real"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "Ver resultados",
                        "text": "Após a conclusão do teste, veja relatórios detalhados e recomendações de performance"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="../static/js/js.js"></script>
    <script src="../static/js/chart.js"></script>
    <script src="../static/js/three.min.js"></script>
    <link rel="stylesheet" href="../static/css/all.min.css">
    <link href="../static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="../static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="../logo.svg" alt="Logo" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader BM Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="#test" class="active nav-link">Teste de Performance</a></li>
                    <li><a href="#about" class="nav-link">Sobre a Ferramenta</a></li>
                    <li><a href="#test-guide" class="nav-link">Guia de Teste</a></li>
                    <li><a href="#technology" class="nav-link">Princípios Técnicos</a></li>
                    <!-- <li><a href="#compatibility" class="nav-link">Compatibilidade</a></li>
                    <li><a href="#gpu-knowledge" class="nav-link">Conhecimento GPU</a></li>
                    <li><a href="#hardware-recommendation" class="nav-link">Recomendações de Hardware</a></li> -->
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="Alterar idioma">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">Português</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">English</a>
                        <a href="../zh-cn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="../ja-jp/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                        <a href="../ko-kr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">한국어</a>
                        <a href="../vi-vn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Tiếng Việt</a>
                        <a href="../it-it/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Italiano</a>
                        <a href="../es-es/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Español</a>
                        <a href="../fr-fr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Français</a>
                        <a href="../de-de/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Deutsch</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">Português</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="Alterar tema">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="Menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Teste de Performance</a></li>
                <li><a href="#about" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Sobre a Ferramenta</a></li>
                <li><a href="#test-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Guia de Teste</a></li>
                <li><a href="#technology" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Princípios Técnicos</a></li>
                <li><a href="#compatibility" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Compatibilidade</a></li>
                <li><a href="#gpu-knowledge" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Conhecimento GPU</a></li> -->
                <li><a href="#hardware-recommendation" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Recomendações de Hardware</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
          
        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader BM Test</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> Ecrã Completo
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> Captura de Ecrã
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">Clique no botão Iniciar Teste para começar a renderização</div>
                                <div class="lg:hidden">Clique no botão abaixo para iniciar o teste GPU</div>
                                <div class="text-sm text-gray-300 mt-1">Certifique-se de que seu dispositivo suporta WebGL</div>
                            </div>
                        </div>
                        
                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">Selecionar Modo de Teste</h3>
                                    </div>
                                    
                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Teste Leve</h4>
                                                        <p class="text-gray-300 text-sm">Teste básico de performance, adequado para dispositivos de baixa performance</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Teste Médio</h4>
                                                        <p class="text-gray-300 text-sm">Teste de carga padrão, adequado para dispositivos de gama média</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Teste Pesado</h4>
                                                        <p class="text-gray-300 text-sm">Teste de alta intensidade, adequado para dispositivos high-end</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Teste Extremo</h4>
                                                        <p class="text-gray-300 text-sm">Teste de stress extremo, usar com cuidado</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            Iniciar Teste
                                        </button>

                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            Parar Teste
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                Iniciar Teste GPU
                            </button>
                        </div>

                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>Tempo Render: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>Triângulos: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>Complexidade: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>

                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">Problema de performance detectado, considere reduzir o nível de teste</span>
                        </div>
                    </div>
                    
                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Informações do Dispositivo</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">A detectar...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Versão WebGL:</span>
                                <span id="webgl-version" class="text-right">A detectar...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Navegador:</span>
                                <span id="browser-info" class="text-right">A detectar...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Sistema Operativo:</span>
                                <span id="os-info" class="text-right">A detectar...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Teste Leve</h3>
                                    <p>Teste básico de performance</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Teste Médio</h3>
                                    <p>Teste de carga padrão</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Teste Pesado</h3>
                                    <p>Teste de alta intensidade</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Teste Extremo</h3>
                                    <p>Teste de stress extremo</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Personalizado</h3>
                                    <p>Teste de parâmetros personalizados</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Controlo de Teste</h3>

                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> Iniciar Teste
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> Parar Teste
                            </button>
                        </div>

                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>Progresso do Teste</span>
                                <span id="progress-text">0/60 segundos</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Número de Triângulos</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Atual: <span id="triangle-value">50.000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Complexidade do Shader</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Atual: <span id="complexity-value">2,5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Duração do Teste (segundos)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Atual: <span id="duration-value">60</span> segundos</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Performance em Tempo Real</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">Pontuação</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">Normal</div>
                                <div class="text-gray-500 dark:text-gray-400">Estado da Temperatura</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">Leve</div>
                                <div class="text-gray-500 dark:text-gray-400">Nível de Teste</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> Partilhar Resultados
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> Histórico de Testes
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Análise de Performance</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">Gráfico FPS</button>
                        <button class="chart-tab" data-chart="score">Tendência de Pontuação</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">FPS Médio</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">FPS Máximo</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">FPS Mínimo</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Estabilidade</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Histórico de Testes</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> Limpar
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>Ainda não há registos de teste</p>
                        <p class="text-sm">Testes de performance concluídos serão exibidos aqui</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Total <span id="history-count">0</span> testes
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> Exportar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Shader BM Introduction Section -->
        <div class="card p-6 mt-6 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                <div class="order-2 md:order-1">
                    <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Descubra o Volume Shader BM</h2>
                    <div class="text-gray-700 dark:text-gray-300 space-y-4">
                        <p>Volume Shader BM é uma ferramenta de benchmark GPU de última geração, desenvolvida para levar seu hardware gráfico ao limite através de renderização complexa de volume shader.</p>
                        <p>Nosso benchmark utiliza shaders de volume 3D avançados para gerar padrões fractais realistas que testam as capacidades de processamento da sua GPU e fornecem métricas de performance precisas.</p>
                        <p>Seja você um gamer que quer avaliar a performance do seu sistema, um profissional que precisa validar capacidades de hardware, ou simplesmente curioso sobre as capacidades gráficas do seu dispositivo, Volume Shader BM oferece resultados precisos e confiáveis.</p>
                        <div class="mt-6">
                            <a href="#test" class="btn-primary inline-flex items-center px-4 py-2 rounded-md">
                                <i class="fas fa-play mr-2"></i> Iniciar Benchmark
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-1 md:order-2">
                    <div class="rounded-lg overflow-hidden shadow-lg transform transition-transform hover:scale-105 duration-300">
                        <img src="../images/og-image.avif" alt="Volume Shader Benchmark Visualization" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- About Tool Area -->
        <div id="about" class="card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Sobre o Volume Shader BM Test</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Volume Shader BM Test é uma ferramenta online de avaliação de performance GPU baseada em WebGL que avalia a performance gráfica do seu dispositivo através da renderização de volume shaders 3D complexos.</p>
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2">Descrição dos Níveis de Teste</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm">Leve: 10K triângulos</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm">Médio: 50K triângulos</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                            <span class="text-sm">Pesado: 200K triângulos</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm">Extremo: 500K triângulos</span>
                        </div>
                    </div>
                </div>
                <p><strong>Nota:</strong> Testes de alta intensidade podem causar atrasos ou aquecimento em dispositivos de baixa performance. Por favor, escolha um nível de teste apropriado baseado na performance do seu dispositivo.</p>
            </div>
        </div>

        <!-- Test Guide Area -->
        <div id="test-guide" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-book-open mr-2 text-primary-500"></i>Guia de Teste GPU
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Preparation -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-clipboard-check mr-2 text-green-500"></i>Preparação do Teste
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Feche programas desnecessários e abas do navegador para liberar recursos do sistema</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Certifique-se de que o seu dispositivo tem boas condições de refrigeração para evitar sobreaquecimento durante o teste</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Use uma fonte de alimentação estável para evitar interrupções durante o teste</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Atualize os drivers gráficos para a versão mais recente para performance otimizada</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Test Steps -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-list-ol mr-2 text-blue-500"></i>Passos do Teste
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                <span>Escolha um modo de teste adequado (recomenda-se começar com Leve)</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                <span>Ajuste a complexidade e duração do teste se necessário</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                <span>Clique em Iniciar Teste e observe os dados de performance em tempo real</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                                <span>Após a conclusão do teste, veja o relatório detalhado e recomendações</span>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- Test Mode Details -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-layer-group mr-2 text-purple-500"></i>Detalhes dos Modos de Teste
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                            <h4 class="font-medium text-green-800 dark:text-green-200">Teste Leve</h4>
                        </div>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• Adequado para dispositivos de escritório e GPUs iniciantes</li>
                            <li>• 10K triângulos, baixa carga GPU</li>
                            <li>• Duração do teste: 30 segundos</li>
                            <li>• Baixa geração de calor, adequado para execuções longas</li>
                        </ul>
                    </div>

                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">Teste Médio</h4>
                        </div>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Adequado para GPUs gaming de gama média</li>
                            <li>• 50K triângulos, carga GPU média</li>
                            <li>• Duração do teste: 60 segundos</li>
                            <li>• Pode detectar gargalos de performance e estabilidade</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200">Teste Pesado</h4>
                        </div>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• Adequado para GPUs gaming high-end</li>
                            <li>• 200K triângulos, alta carga GPU</li>
                            <li>• Duração do teste: 90 segundos</li>
                            <li>• Testa sistema de refrigeração e estabilidade da fonte</li>
                        </ul>
                    </div>

                    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                            <h4 class="font-medium text-red-800 dark:text-red-200">Teste Extremo</h4>
                        </div>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• Adequado para GPUs profissionais high-end</li>
                            <li>• 500K triângulos, carga GPU extrema</li>
                            <li>• Duração do teste: 120 segundos</li>
                            <li>• Teste de stress, pode causar aquecimento do dispositivo</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Results Interpretation Guide -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-indigo-500"></i>Guia de Interpretação de Resultados
                </h3>
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">Análise FPS</h4>
                            <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                                <li>• <strong>60+ FPS</strong>: Performance excelente</li>
                                <li>• <strong>30-60 FPS</strong>: Boa performance</li>
                                <li>• <strong>15-30 FPS</strong>: Performance média</li>
                                <li>• <strong>&lt;15 FPS</strong>: Performance baixa</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">Estado da Temperatura</h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• <strong>Normal</strong>: Temperatura GPU estável</li>
                                <li>• <strong>Morno</strong>: Ligeiro aumento de temperatura, faixa normal</li>
                                <li>• <strong>Quente</strong>: Refrigeração necessária</li>
                                <li>• <strong>Sobreaquecimento</strong>: Recomenda-se parar o teste</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">Pontuação Total</h4>
                            <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                                <li>• <strong>9000+</strong>: Performance flagship</li>
                                <li>• <strong>6000-9000</strong>: Performance high-end</li>
                                <li>• <strong>3000-6000</strong>: Performance gama média</li>
                                <li>• <strong>&lt;3000</strong>: Performance entrada</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Important Notes -->
            <div class="mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Notas Importantes</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Preste atenção ao nível da bateria e calor ao executar testes de alta intensidade em dispositivos móveis</li>
                                <li>• Se ocorrerem artefatos de tela ou falhas do sistema durante o teste, pare o teste imediatamente</li>
                                <li>• Os resultados do teste servem apenas como referência; a performance real de jogos também é influenciada por CPU, memória e outros fatores</li>
                                <li>• Testes regulares podem ajudar a monitorizar mudanças na performance da GPU e detectar problemas de hardware precocemente</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Principles Area -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Princípios Tecnológicos</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Esta ferramenta utiliza tecnologia WebGL 2.0 avançada e algoritmos de volume shader para implementar testes de performance GPU através das seguintes técnicas:</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>Tecnologia de Renderização Volumétrica</strong>: Usa funções matemáticas complexas para gerar texturas de volume 3D que simulam a forma e textura de cogumelos venenosos</li>
                    <li><strong>Shaders Dinâmicos</strong>: Ajusta dinamicamente a complexidade do fragment shader baseado no nível de teste, aumentando a carga computacional da GPU</li>
                    <li><strong>Monitorização de Performance em Tempo Real</strong>: Monitora métricas chave como FPS e tempo de renderização através de extensões WebGL e Performance API</li>
                    <li><strong>Algoritmo de Inferência de Temperatura</strong>: Deduz o estado de temperatura do dispositivo através de curvas de degradação de performance</li>
                </ul>
                <p>O algoritmo de teste aumenta gradualmente a complexidade de renderização até atingir o limite de performance do dispositivo, avaliando assim com precisão a capacidade de processamento da GPU.</p>
            </div>
        </div>

        <!-- Compatibility Area -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Compatibilidade de Navegadores</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Totalmente suportado. Versão mais recente recomendada para melhor performance.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Totalmente suportado com excelente performance.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Suporte básico, algumas funcionalidades avançadas podem ser limitadas.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Totalmente suportado, performance similar ao Chrome.</p>
                </div>
            </div>
        </div>

        <!-- GPU Knowledge Base Area -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>Base de Conhecimento GPU
            </h2>

            <!-- GPU Basic Knowledge -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>Fundamentos de GPU
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">O que é uma GPU</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">Graphics Processing Unit (Unidade de Processamento Gráfico), um chip especializado no processamento de renderização gráfica e computação paralela, com milhares de núcleos pequenos que são excelentes em lidar com muitas tarefas simples simultaneamente.</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPU vs CPU</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPUs têm menos núcleos mas mais poderosos, adequados para operações lógicas complexas; GPUs têm muitos núcleos simples, ideais para computação paralela e renderização gráfica. Trabalham juntos para performance otimizada.</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">Tipos de GPU</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">GPUs Integradas: Incorporadas na CPU, baixo consumo de energia mas performance limitada; GPUs Dedicadas: Chips GPU separados, performance poderosa, adequadas para gaming e trabalho profissional.</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Parameters Analysis -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>Análise de Parâmetros Chave
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Memória de Vídeo (VRAM)</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Memória dedicada para armazenar dados gráficos</li>
                                <li>• Capacidade influencia performance de gaming em alta resolução</li>
                                <li>• 8GB+ recomendado para jogos mainstream</li>
                                <li>• 12GB+ necessário para gaming 4K ou trabalho profissional</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Clock do Núcleo</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Clock Base: Frequência de operação estável da GPU</li>
                                <li>• Clock Boost: Frequência após overclock automático</li>
                                <li>• Frequência mais alta significa poder computacional mais forte</li>
                                <li>• Pode ser melhorado através de overclock</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Stream Processors</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Unidades centrais que executam computação paralela</li>
                                <li>• Mais unidades significam capacidade de processamento paralelo mais forte</li>
                                <li>• Na NVIDIA são chamados de núcleos CUDA</li>
                                <li>• Na AMD são chamados de Stream Processors (SP)</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Largura de Bus & Largura de Banda</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Largura do bus de memória influencia transferência de dados</li>
                                <li>• 256-Bit e superior é configuração high-end</li>
                                <li>• Largura de banda = Largura do bus × Frequência da memória</li>
                                <li>• Alta largura de banda reduz gargalos de performance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU Architecture Evolution -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>Evolução da Arquitetura GPU
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>Arquitetura NVIDIA
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">Série RTX 40</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">Série RTX 30</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">Série RTX 20</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">Série GTX 10</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>Arquitetura AMD
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">Série RX 7000</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">Série RX 6000</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">Série RX 5000</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">Série RX 500</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Factors Affecting GPU Performance -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>Fatores que Afetam a Performance da GPU
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Controlo de Temperatura</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Sobreaquecimento causa throttling e prejudica a performance</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Alimentação</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Alimentação insuficiente limita a performance da GPU</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Drivers</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Drivers mais recentes otimizam a performance de jogos</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Configuração do Sistema</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPU e RAM também influenciam a performance geral</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>Guia de Recomendações GPU
            </h2>

            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>Recomendações Baseadas em Orçamento
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">Nível Básico</h4>
                            <!-- <p class="text-sm text-green-600 dark:text-green-400">€300-500</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Gaming 1080p em alta qualidade</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Recomendado ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Melhor escolha custo-benefício</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Recomendado ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>Adequado para:</strong> Gaming 1080p, criação de conteúdo leve, trabalho de escritório diário</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">Gama Média</h4>
                            <!-- <p class="text-sm text-blue-600 dark:text-blue-400">€500-800</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1440p alta qualidade</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Recomendado ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Grande vantagem de VRAM</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Recomendado ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>Adequado para:</strong> Gaming 1440p, edição de vídeo, live streaming</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">Gama Alta</h4>
                            <!-- <p class="text-sm text-purple-600 dark:text-purple-400">€800-1500</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Potência para gaming 4K</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Recomendado ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">24GB de VRAM grande</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Recomendado ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>Adequado para:</strong> Gaming 4K, renderização profissional, computação IA</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">Topo de Gama</h4>
                            <!-- <p class="text-sm text-orange-600 dark:text-orange-400">€1500+</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Performance suprema</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Recomendado ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080 Super</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Topo de gama mais acessível</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Recomendado ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080-super">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Compra
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>Adequado para:</strong> Gaming 8K, workstations, entusiastas</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Usage-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-tasks mr-2 text-blue-500"></i>Recomendações Baseadas em Uso
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Gaming GPUs -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                            <h4 class="font-semibold text-red-800 dark:text-red-200">GPUs para Gaming</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Gaming Esports</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060, RX 7600</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">Alto FPS 1080p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Títulos AAA</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7700 XT</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">1440p alta qualidade</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Gaming 4K</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">Configurações 4K Ultra</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>

                    <!-- Content Creation -->
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">Criação de Conteúdo</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Edição de Vídeo</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7900 GRE</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Aceleração de codificação por hardware</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Renderização 3D</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Aceleração CUDA/OptiX</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Live Streaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060 e superior</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Codificação NVENC</div>
                                 <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                     <i class="fas fa-shopping-cart mr-1"></i>Ver Série RTX 4060
                                 </a>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Professional Work -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200">Trabalho Profissional</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Desenvolvimento IA</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Alta necessidade de VRAM</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Design CAD</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, Série Quadro</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Otimização de drivers profissionais</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                         <i class="fas fa-shopping-cart mr-1"></i>Série Quadro
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Computação Científica</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Ponto flutuante de dupla precisão</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                         <i class="fas fa-shopping-cart mr-1"></i>A5000
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Advice -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>Conselhos de Compra
                </h3>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                                Considerações Importantes
                            </h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Garantir potência suficiente da fonte de alimentação</li>
                                <li>• Verificar espaço no gabinete e refrigeração</li>
                                <li>• Considerar balanceamento de performance da CPU</li>
                                <li>• Atentar aos requisitos de capacidade VRAM</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                                <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                                Melhor Altura para Comprar
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• 3-6 meses após lançamento do produto</li>
                                <li>• Black Friday e outros eventos de compras</li>
                                <li>• Após declínios de crypto mining</li>
                                <li>• Observação racional do mercado usado</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                                Seleção de Marca
                            </h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                                <li>• ASUS ROG: Primeira escolha para gamers high-end</li>
                                <li>• MSI Gaming: Relação preço-performance equilibrada</li>
                                <li>• GIGABYTE AORUS: Excelente refrigeração</li>
                                <li>• Galax: Bom valor para iniciantes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Perguntas Frequentes</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Por que o meu dispositivo aquece durante o teste?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Isso é normal. O teste Toxic Mushroom faz a sua GPU trabalhar com capacidade total, portanto a geração de calor é inevitável. Se a temperatura ficar muito alta, recomendamos parar o teste ou reduzir o nível de teste.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">O que significa a pontuação no resultado do teste?</h3>
                    <p class="text-gray-700 dark:text-gray-300">A pontuação considera FPS, estabilidade de renderização e nível de teste. Uma pontuação mais alta indica performance mais forte da GPU e pode ser usada para comparação direta com outros dispositivos.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">O que fazer se o meu navegador mostrar que WebGL não é suportado?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Por favor, certifique-se de que o seu navegador está atualizado, verifique se a aceleração de hardware está ativada, ou tente outro navegador. Alguns dispositivos móveis podem não suportar WebGL 2.0.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Os meus dados de teste são enviados?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Todos os dados de teste são armazenados localmente e não são enviados para um servidor. Pode executar testes com segurança sem se preocupar com problemas de privacidade.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">Ferramenta profissional online de teste de performance GPU</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">Volume Shader BM Test</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Links Relacionados</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Manual do Utilizador</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Suporte Técnico</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Feedback</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Aviso Legal</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Esta ferramenta serve exclusivamente para testar a performance da GPU. O aquecimento do dispositivo e consumo de energia durante o uso são fenómenos normais. Por favor, escolha um nível de teste apropriado baseado nas capacidades do seu dispositivo.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../static/js/app.js"></script>
    
    <!-- Script de Seleção de Idioma -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');

            // Mostrar/ocultar menu de seleção de idioma
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });

            // Fechar menu dropdown quando clicar noutro local da página
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>