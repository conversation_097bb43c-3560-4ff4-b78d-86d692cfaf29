<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | Volume Shader BM Test</title>
    <meta name="description" content="The page you're looking for doesn't exist. Return to Volume Shader BM Test for GPU performance benchmarking.">
    <link rel="icon" href="logo.svg" type="image/svg+xml">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link href="static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom animations and effects */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
            50% { box-shadow: 0 0 40px rgba(99, 102, 241, 0.6); }
        }
        
        @keyframes pulse-glow {
            0%, 100% { 
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
                transform: scale(1);
            }
            50% { 
                text-shadow: 0 0 40px rgba(99, 102, 241, 0.8);
                transform: scale(1.05);
            }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
        
        .glow-effect {
            animation: glow 2s ease-in-out infinite;
        }
        
        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #1e40af 0%, #5b21b6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #2563eb, #7c3aed);
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .dark .card-hover:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #6d28d9 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.4);
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            animation: particle-float 6s infinite linear;
        }
        
        @keyframes particle-float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200 overflow-x-hidden">
    <!-- Animated Background Particles -->
    <div id="particles" class="fixed inset-0 pointer-events-none z-0"></div>
    
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 glass-effect shadow-lg">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <img src="logo.svg" alt="Logo" class="w-8 h-8 floating">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full opacity-20 animate-ping"></div>
                    </div>
                    <h1 class="text-xl font-bold gradient-text">Volume Shader BM Test</h1>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">
                        <i class="fas fa-home mr-1"></i>Home
                    </a>
                    <a href="/#test" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">
                        <i class="fas fa-tachometer-alt mr-1"></i>Performance Test
                    </a>
                    <a href="/#about" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">
                        <i class="fas fa-info-circle mr-1"></i>About Tool
                    </a>
                    <a href="/#test-guide" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">
                        <i class="fas fa-book-open mr-1"></i>Test Guide
                    </a>
                    <a href="/#technology" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">
                        <i class="fas fa-microchip mr-1"></i>Technology
                    </a>
                    <a href="/#faq" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">
                        <i class="fas fa-question-circle mr-1"></i>FAQ
                    </a>
                </nav>
                
                <!-- Right Side Controls -->
                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors cursor-pointer" id="theme-toggle" title="Switch Theme">
                        <i class="fas fa-moon"></i>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors cursor-pointer" id="mobile-menu-btn" title="Menu">
                        <i class="fas fa-bars"></i>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Navigation Menu -->
            <div class="mobile-nav hidden bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-4 px-4 md:hidden">
                <div class="grid grid-cols-2 gap-4">
                    <a href="/" class="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-home text-primary-500 mr-3"></i>
                        <span>Home</span>
                    </a>
                    <a href="/#test" class="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-tachometer-alt text-blue-500 mr-3"></i>
                        <span>Performance Test</span>
                    </a>
                    <a href="/#about" class="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-info-circle text-green-500 mr-3"></i>
                        <span>About Tool</span>
                    </a>
                    <a href="/#test-guide" class="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-book-open text-purple-500 mr-3"></i>
                        <span>Test Guide</span>
                    </a>
                    <a href="/#technology" class="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-microchip text-orange-500 mr-3"></i>
                        <span>Technology</span>
                    </a>
                    <a href="/#faq" class="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-question-circle text-red-500 mr-3"></i>
                        <span>FAQ</span>
                    </a>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container mx-auto px-4 py-8">
        <!-- 404 Error Section -->
        <div class="min-h-[70vh] flex items-center justify-center">
            <div class="text-center max-w-4xl mx-auto">
                <!-- 404 Number with Enhanced GPU Theme -->
                <div class="mb-12 relative">
                    <div class="relative inline-block">
                        <h1 class="text-8xl md:text-[10rem] lg:text-[12rem] font-black pulse-glow">
                            <span class="bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 bg-clip-text text-transparent">4</span>
                            <span class="bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 bg-clip-text text-transparent">0</span>
                            <span class="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 bg-clip-text text-transparent">4</span>
                        </h1>
                        
                        <!-- GPU Icon Overlay with Animation -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="relative">
                                <i class="fas fa-microchip text-4xl md:text-6xl lg:text-8xl text-gray-300/30 dark:text-gray-600/30 floating"></i>
                                <div class="absolute inset-0 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full opacity-20 animate-ping"></div>
                            </div>
                        </div>
                        
                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full opacity-60 animate-bounce"></div>
                        <div class="absolute -bottom-4 -right-4 w-6 h-6 bg-gradient-to-r from-secondary-400 to-purple-400 rounded-full opacity-60 animate-bounce" style="animation-delay: 0.5s;"></div>
                    </div>
                </div>
                
                <!-- Error Message Card -->
                <div class="card glass-effect p-8 md:p-12 mb-12 card-hover glow-effect">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold gradient-text mb-6">
                        <i class="fas fa-exclamation-triangle mr-3 text-yellow-500"></i>
                        Page Not Found
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
                        The GPU shader you're looking for seems to have failed to render. 
                        <br class="hidden md:block">
                        This page doesn't exist in our volume rendering pipeline.
                    </p>
                    
                    <!-- Error Details with Enhanced Design -->
                    <div class="bg-gradient-to-r from-red-50 via-orange-50 to-yellow-50 dark:from-red-900/20 dark:via-orange-900/20 dark:to-yellow-900/20 rounded-xl p-6 border border-red-200 dark:border-red-700 mb-8">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div class="flex items-center justify-center space-x-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                <span class="text-gray-700 dark:text-gray-300">Error Code: <strong>404</strong></span>
                            </div>
                            <div class="flex items-center justify-center space-x-2">
                                <div class="w-3 h-3 bg-orange-500 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                                <span class="text-gray-700 dark:text-gray-300">Time: <strong id="current-time"></strong></span>
                            </div>
                            <div class="flex items-center justify-center space-x-2">
                                <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                                <span class="text-gray-700 dark:text-gray-300">Status: <strong>Not Found</strong></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Possible Causes with Enhanced Cards -->
                    <div class="mb-12">
                        <h3 class="text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6 flex items-center justify-center">
                            <i class="fas fa-search mr-3 text-primary-500"></i>
                            Possible Causes
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-white/70 dark:bg-gray-800/70 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-hover">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-link text-blue-500 text-xl"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Broken Link</h4>
                                        <p class="text-gray-600 dark:text-gray-400">The link you followed may be outdated or incorrect</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/70 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-hover">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-edit text-green-500 text-xl"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Typo in URL</h4>
                                        <p class="text-gray-600 dark:text-gray-400">Check if there's a typo in the address bar</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/70 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-hover">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-trash text-red-500 text-xl"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Page Removed</h4>
                                        <p class="text-gray-600 dark:text-gray-400">The page may have been moved or deleted</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/70 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-hover">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-server text-purple-500 text-xl"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Server Issue</h4>
                                        <p class="text-gray-600 dark:text-gray-400">Temporary server problem (unlikely)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons with Enhanced Design -->
                <div class="space-y-6 mb-12">
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                                 <a href="/" class="bg-gradient-to-r from-blue-600 to-purple-700 hover:from-blue-700 hover:to-purple-800 flex items-center justify-center px-8 py-4 text-lg font-semibold text-white rounded-xl shadow-lg transition-all duration-300 hover:transform hover:scale-105 border border-blue-500/20">
                             <i class="fas fa-home mr-3"></i>
                             Return to Home
                         </a>
                         <a href="/#test" class="bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 flex items-center justify-center px-8 py-4 text-lg font-semibold text-white rounded-xl shadow-lg transition-all duration-300 hover:transform hover:scale-105 border border-green-500/20">
                             <i class="fas fa-play mr-3"></i>
                             Start GPU Test
                         </a>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="history.back()" class="bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 flex items-center justify-center px-6 py-3 rounded-xl shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700">
                            <i class="fas fa-arrow-left mr-2 text-gray-600 dark:text-gray-400"></i>
                            <span class="text-gray-700 dark:text-gray-300">Go Back</span>
                        </button>
                        <button onclick="window.location.reload()" class="bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 flex items-center justify-center px-6 py-3 rounded-xl shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700">
                            <i class="fas fa-redo mr-2 text-gray-600 dark:text-gray-400"></i>
                            <span class="text-gray-700 dark:text-gray-300">Refresh Page</span>
                        </button>
                    </div>
                </div>
                
                <!-- Quick Links with Enhanced Design -->
                <div class="mb-12">
                    <h3 class="text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">
                        <i class="fas fa-star mr-3 text-yellow-500"></i>
                        Popular Pages
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <a href="/#test" class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700 card-hover group">
                            <div class="flex flex-col items-center text-center">
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                                    <i class="fas fa-tachometer-alt text-blue-500 text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Performance Test</span>
                            </div>
                        </a>
                        <a href="/#about" class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4 border border-green-200 dark:border-green-700 card-hover group">
                            <div class="flex flex-col items-center text-center">
                                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                                    <i class="fas fa-info-circle text-green-500 text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">About Tool</span>
                            </div>
                        </a>
                        <a href="/#test-guide" class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4 border border-purple-200 dark:border-purple-700 card-hover group">
                            <div class="flex flex-col items-center text-center">
                                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                                    <i class="fas fa-book-open text-purple-500 text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Test Guide</span>
                            </div>
                        </a>
                        <a href="/#faq" class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-4 border border-orange-200 dark:border-orange-700 card-hover group">
                            <div class="flex flex-col items-center text-center">
                                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                                    <i class="fas fa-question-circle text-orange-500 text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">FAQ</span>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- GPU Performance Tip with Enhanced Design -->
                <div class="bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-900/20 dark:via-purple-900/20 dark:to-pink-900/20 rounded-2xl p-8 border border-indigo-200 dark:border-indigo-700 card-hover">
                    <div class="flex flex-col md:flex-row items-center text-center md:text-left">
                        <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                                <i class="fas fa-lightbulb text-white text-2xl"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-3">GPU Performance Tip</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                                While you're here, why not test your GPU performance? Our Volume Shader BM Test can help you understand your graphics card's capabilities and identify potential performance bottlenecks.
                            </p>
                                                         <a href="/#test" class="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-700 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-800 transition-all duration-300 shadow-lg hover:transform hover:scale-105 border border-blue-500/20">
                                 <i class="fas fa-rocket mr-2"></i>
                                 Start Benchmarking Now
                                 <i class="fas fa-arrow-right ml-2"></i>
                             </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Enhanced Footer -->
    <footer class="mt-16 py-12 border-t border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 mb-6">
                    <div class="relative">
                        <img src="logo.svg" alt="Logo" class="w-8 h-8 floating">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full opacity-20 animate-ping"></div>
                    </div>
                    <span class="text-xl font-bold gradient-text">Volume Shader BM Test</span>
                </div>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Professional online GPU performance benchmark tool
                </p>
                <div class="flex flex-wrap justify-center gap-4 mb-6">
                    <a href="/" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                        <i class="fas fa-home mr-1"></i>Home
                    </a>
                    <a href="/#test" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                        <i class="fas fa-tachometer-alt mr-1"></i>Test
                    </a>
                    <a href="/#about" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                        <i class="fas fa-info-circle mr-1"></i>About
                    </a>
                    <a href="/#faq" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                        <i class="fas fa-question-circle mr-1"></i>FAQ
                    </a>
                </div>
                <p class="text-gray-500 dark:text-gray-500 text-sm">
                    © 2025 Volume Shader BM Test. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <script src="static/js/js.js"></script>
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Update time immediately and every second
        updateTime();
        setInterval(updateTime, 1000);
        
        // Theme toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const html = document.documentElement;
            
            // Check for saved theme preference or default to light mode
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                html.classList.add('dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            }
            
            // Theme toggle click handler
            themeToggle.addEventListener('click', function() {
                html.classList.toggle('dark');
                const isDark = html.classList.contains('dark');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
                themeToggle.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
            });
            
            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileNav = document.querySelector('.mobile-nav');
            
            mobileMenuBtn.addEventListener('click', function() {
                mobileNav.classList.toggle('hidden');
            });
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!mobileMenuBtn.contains(event.target) && !mobileNav.contains(event.target)) {
                    mobileNav.classList.add('hidden');
                }
            });
        });
        
        // Create animated particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        
        // Initialize particles
        document.addEventListener('DOMContentLoaded', createParticles);
        
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe all cards for animation
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card-hover');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html> 