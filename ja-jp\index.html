<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM Test - オンラインGPUパフォーマンスベンチマークツール</title>
    <meta name="description" content="複雑な3Dボリュームシェーダーレンダリングを通じてグラフィックカードのパフォーマンスをテストする専門的なオンラインGPUパフォーマンステストツールで、リアルタイムFPSモニタリングとパフォーマンス分析をサポートします。">
    <meta name="keywords" content="グラフィックカードテスト,GPUテスト,ボリュームシェーダー,WebGL,FPSテスト">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/ja-jp/">
    <link rel="icon" href="../logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="../logo.svg">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/ja-jp/">
    <meta property="og:title" content="Volume Shader BM Test | オンラインGPUパフォーマンスベンチマークツール">
    <meta property="og:description" content="複雑な3Dボリュームシェーダーレンダリングを通じてグラフィックカードのパフォーマンスをテストする専門的なオンラインGPUパフォーマンステストツールで、リアルタイムFPSモニタリングとパフォーマンス分析をサポートします。">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/ja-jp/">
    <meta property="twitter:title" content="Volume Shader BM Test | オンラインGPUパフォーマンスベンチマークツール">
    <meta property="twitter:description" content="複雑な3Dボリュームシェーダーレンダリングを通じてグラフィックカードのパフォーマンスをテストする専門的なオンラインGPUパフォーマンステストツールで、リアルタイムFPSモニタリングとパフォーマンス分析をサポートします。">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/twitter-image.jpg">
    
    <!-- 構造化データ -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#website",
                "url": "https://volumeshaderbmtest.com/ja-jp/",
                "name": "Volume Shader BM Test",
                "description": "専門的なオンラインGPUパフォーマンスベンチマークツール",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/ja-jp/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/ja-jp/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#organization",
                "name": "Toxic Mushroom Test Team",
                "url": "https://volumeshaderbmtest.com/ja-jp/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#software",
                "name": "Volume Shader BM Test",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/ja-jp/",
                "description": "WebGLベースのオンラインGPUパフォーマンスベンチマークツールで、ライト、ミディアム、ヘビー、エクストリームの4つのテストモードをサポートし、FPS、温度、その他の主要なパフォーマンスメトリクスをリアルタイムでモニタリングします。",
                "featureList": [
                    "GPUパフォーマンステスト",
                    "リアルタイムFPSモニタリング",
                    "温度状態モニタリング",
                    "パフォーマンス履歴記録",
                    "テスト結果分析",
                    "WebGLボリュームレンダリング",
                    "複数の難易度テストモード"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "JPY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#gpu-knowledge",
                "headline": "GPU知識ベース - 完全なグラフィックカード技術ガイド",
                "description": "GPUの基本、主要パラメータ、アーキテクチャの発展、パフォーマンスに影響を与える要因についての詳細な紹介で、グラフィックカードの選択とパフォーマンスの最適化のための専門的なガイダンスを提供します。",
                "author": {
                    "@type": "Organization",
                    "name": "Toxic Mushroom Test Team"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/ja-jp/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/ja-jp/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "テスト中にデバイスが熱くなるのはなぜですか？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "これは正常です。Toxic Mushroomテストはフル容量でGPUを動作させるため、発熱は避けられません。温度が高すぎる場合は、テストを停止するか、テストレベルを下げることをお勧めします。"
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "テスト結果のスコアは何を表していますか？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "スコアはFPS、レンダリングの安定性、テストレベルを考慮しています。より高いスコアはより強力なGPUパフォーマンスを表し、他のデバイスと水平比較できます。"
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "ブラウザがWebGLをサポートしていないと表示された場合はどうすればよいですか？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "ブラウザが最新バージョンであることを確認し、ハードウェアアクセラレーションが有効になっているかどうかを確認するか、ブラウザを切り替えてみてください。一部のモバイルデバイスはWebGL 2.0をサポートしていない場合があります。"
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "テストデータはアップロードされますか？",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "すべてのテストデータはローカルに保存され、サーバーにアップロードされることはありません。プライバシーの問題を心配することなく、安全にテストを実施できます。"
                        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#gpu-recommendations",
                "name": "グラフィックカード推奨ガイド",
                "description": "予算と目的によって分類された専門的なグラフィックカードの推奨",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
                        "description": "エントリーレベルのゲーミンググラフィックカード、1080p高品質ゲームに適しています",
                        "category": "グラフィックカード",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "JPY",
                            "lowPrice": "30000",
                            "highPrice": "40000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
                        "description": "最高のバリューチョイス、推奨エントリーレベルグラフィックカード",
                        "category": "グラフィックカード",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "JPY",
                            "lowPrice": "28000",
                            "highPrice": "36000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
                        "description": "ミッドレンジゲーミンググラフィックカード、1440p高品質ゲームをサポート",
                        "category": "グラフィックカード",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "JPY",
                            "lowPrice": "60000",
                            "highPrice": "70000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 4,
                        "name": "NVIDIA GeForce RTX 4090",
                        "description": "フラッグシップグラフィックカード、究極の4Kゲーミングパフォーマンス",
                        "category": "グラフィックカード",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "JPY",
                            "lowPrice": "240000",
                            "highPrice": "300000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/ja-jp/#test-guide",
                "name": "GPUパフォーマンステストガイド",
                "description": "詳細なGPUパフォーマンステストのステップとベストプラクティス",
                "image": "https://volumeshaderbmtest.com/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "WebGL互換ブラウザ"
                    },
                    {
                        "@type": "HowToSupply", 
                        "name": "専用または統合グラフィックカード"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "Volume Shader BM Test ツール"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "テスト前の準備",
                        "text": "不要なプログラムとブラウザタブを閉じ、デバイスが良好な放熱環境を持っていることを確認します"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "テストモードの選択",
                        "text": "デバイスのパフォーマンスに応じて適切なテストモードを選択し、ライトモードから始めることをお勧めします"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "テスト開始",
                        "text": "テスト開始をクリックし、リアルタイムのパフォーマンスデータの変化を観察します"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "結果の表示",
                        "text": "テスト完了後、詳細なレポートとパフォーマンスの推奨事項を表示します"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="../static/js/js.js"></script>
    <script src="../static/js/chart.js"></script>
    <script src="../static/js/three.min.js"></script>
    <link rel="stylesheet" href="../static/css/all.min.css">
    <link href="../static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="../static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans JP', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="../logo.svg" alt="ロゴ" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader BM Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="#test" class="active nav-link">パフォーマンステスト</a></li>
                    <li><a href="#about" class="nav-link">ツールについて</a></li>
                    <li><a href="#test-guide" class="nav-link">テストガイド</a></li>
                    <li><a href="#technology" class="nav-link">技術原理</a></li>
                    <!-- <li><a href="#compatibility" class="nav-link">互換性</a></li>
                    <li><a href="#gpu-knowledge" class="nav-link">GPU知識</a></li>
                    <li><a href="#hardware-recommendation" class="nav-link">ハードウェア推奨</a></li> -->
                    <li><a href="#faq" class="nav-link">よくある質問</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="言語切替">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">日本語</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">English</a>
                        <a href="../zh-cn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">日本語</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="テーマ切替">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="メニュー">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">パフォーマンステスト</a></li>
                <li><a href="#about" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">ツールについて</a></li>
                <li><a href="#test-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">テストガイド</a></li>
                <li><a href="#technology" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">技術原理</a></li>
                <li><a href="#compatibility" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">互換性</a></li>
                <li><a href="#gpu-knowledge" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">GPU知識</a></li> -->
                <li><a href="#hardware-recommendation" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">ハードウェア推奨</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">よくある質問</a></li>
            </ul>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
          
        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader BM Test</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> 全画面
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> スクリーンショット
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">テスト開始ボタンをクリックしてレンダリングを開始</div>
                                <div class="lg:hidden">下のボタンをクリックしてGPUテストを開始</div>
                                <div class="text-sm text-gray-300 mt-1">お使いのデバイスがWebGLをサポートしていることを確認してください</div>
                            </div>
                        </div>
                        
                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                
                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">テストモードを選択</h3>
                                    </div>
                                    
                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">ライトテスト</h4>
                                                        <p class="text-gray-300 text-sm">基本的なパフォーマンステスト、低スペックデバイスに適しています</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">ミディアムテスト</h4>
                                                        <p class="text-gray-300 text-sm">標準的な負荷テスト、中程度のスペックデバイスに適しています</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">ヘビーテスト</h4>
                                                        <p class="text-gray-300 text-sm">高負荷テスト、高スペックデバイスに適しています</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">エクストリームテスト</h4>
                                                        <p class="text-gray-300 text-sm">極限ストレステスト、注意して使用してください</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            テスト開始
                                        </button>
                                        
                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            テスト停止
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                GPUテスト開始
                            </button>
                        </div>
                        
                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>レンダリング時間: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>三角形数: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>複雑度: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>
                        
                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">パフォーマンスの問題が検出されました、テストレベルを下げることをお勧めします</span>
                        </div>
                    </div>
                    
                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">デバイス情報</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">検出中...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>WebGLバージョン:</span>
                                <span id="webgl-version" class="text-right">検出中...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>ブラウザ:</span>
                                <span id="browser-info" class="text-right">検出中...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>オペレーティングシステム:</span>
                                <span id="os-info" class="text-right">検出中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>ライトテスト</h3>
                                    <p>基本的なパフォーマンステスト</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>ミディアムテスト</h3>
                                    <p>標準的な負荷テスト</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>ヘビーテスト</h3>
                                    <p>高負荷テスト</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>エクストリームテスト</h3>
                                    <p>極限ストレステスト</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>カスタム</h3>
                                    <p>カスタムパラメータテスト</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">テストコントロール</h3>
                        
                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> テスト開始
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> テスト停止
                            </button>
                        </div>
                        
                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>テスト進捗</span>
                                <span id="progress-text">0/60秒</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">三角形数</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">現在: <span id="triangle-value">50,000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">シェーダー複雑度</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">現在: <span id="complexity-value">2.5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">テスト時間（秒）</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">現在: <span id="duration-value">60</span>秒</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">リアルタイムパフォーマンス</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">スコア</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">正常</div>
                                <div class="text-gray-500 dark:text-gray-400">温度状態</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">ライト</div>
                                <div class="text-gray-500 dark:text-gray-400">テストレベル</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> 結果を共有
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> テスト履歴
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Mode Details -->
        <div class="mt-6">
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-layer-group mr-2 text-purple-500"></i>テストモード詳細
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                    <div class="flex items-center mb-3">
                        <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                        <h4 class="font-medium text-green-800 dark:text-green-200">ライトテスト</h4>
                    </div>
                    <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                        <li>• オフィス用デバイスやエントリーレベルGPUに適しています</li>
                        <li>• 10K三角形、低GPU負荷</li>
                        <li>• テスト時間：30秒</li>
                        <li>• 発熱が少なく、長時間の実行に適しています</li>
                    </ul>
                </div>
                
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                    <div class="flex items-center mb-3">
                        <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                        <h4 class="font-medium text-blue-800 dark:text-blue-200">ミディアムテスト</h4>
                    </div>
                    <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• ミッドレンジゲーミングGPUに適しています</li>
                        <li>• 50K三角形、中程度のGPU負荷</li>
                        <li>• テスト時間：60秒</li>
                        <li>• パフォーマンスのボトルネックと安定性を検出できます</li>
                    </ul>
                </div>
                
                <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                    <div class="flex items-center mb-3">
                        <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                        <h4 class="font-medium text-orange-800 dark:text-orange-200">ヘビーテスト</h4>
                    </div>
                    <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                        <li>• ハイエンドゲーミングGPUに適しています</li>
                        <li>• 200K三角形、高GPU負荷</li>
                        <li>• テスト時間：90秒</li>
                        <li>• 冷却システムと電源の安定性をテストします</li>
                    </ul>
                </div>
                
                <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                    <div class="flex items-center mb-3">
                        <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                        <h4 class="font-medium text-red-800 dark:text-red-200">エクストリームテスト</h4>
                    </div>
                    <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                        <li>• トップティアのプロフェッショナルGPUに適しています</li>
                        <li>• 500K三角形、極限GPU負荷</li>
                        <li>• テスト時間：120秒</li>
                        <li>• ストレステスト、デバイスの発熱を引き起こす可能性があります</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Results Interpretation Guide -->
        <div class="mt-6">
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-chart-line mr-2 text-indigo-500"></i>結果解釈ガイド
            </h3>
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">FPS分析</h4>
                        <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                            <li>• <strong>60+ FPS</strong>: 優れたパフォーマンス</li>
                            <li>• <strong>30-60 FPS</strong>: 良好なパフォーマンス</li>
                            <li>• <strong>15-30 FPS</strong>: 平均的なパフォーマンス</li>
                            <li>• <strong>&lt;15 FPS</strong>: 低パフォーマンス</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">温度状態</h4>
                        <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                            <li>• <strong>正常</strong>: GPU温度が安定</li>
                            <li>• <strong>温かい</strong>: わずかな温度上昇、正常範囲内</li>
                            <li>• <strong>高温</strong>: 冷却に注意が必要</li>
                            <li>• <strong>過熱</strong>: テストの停止を推奨</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">総合スコア</h4>
                        <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                            <li>• <strong>9000+</strong>: フラッグシップパフォーマンス</li>
                            <li>• <strong>6000-9000</strong>: ハイエンドパフォーマンス</li>
                            <li>• <strong>3000-6000</strong>: ミッドレンジパフォーマンス</li>
                            <li>• <strong>&lt;3000</strong>: エントリーレベルパフォーマンス</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">パフォーマンス分析</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPSチャート</button>
                        <button class="chart-tab" data-chart="score">スコア傾向</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">平均FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">最大FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">最小FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">安定性</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">テスト履歴</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> クリア
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>まだテスト記録がありません</p>
                        <p class="text-sm">完了したパフォーマンステストがここに表示されます</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        合計 <span id="history-count">0</span> テスト
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> エクスポート
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Shader BM Introduction Section -->
        <div class="card p-6 mt-6 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                <div class="order-2 md:order-1">
                    <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BMを発見</h2>
                    <div class="text-gray-700 dark:text-gray-300 space-y-4">
                        <p>Volume Shader BMは、複雑なボリューメトリックシェーダーレンダリングを通じてグラフィックスハードウェアを限界まで押し上げるように設計された最先端のGPUベンチマークツールです。</p>
                        <p>当社のベンチマークは、高度な3Dボリュームシェーダーを使用して、GPUの処理能力をストレステストする現実的なフラクタルパターンを作成し、正確なパフォーマンスメトリクスを提供します。</p>
                        <p>システムのパフォーマンスを評価したいゲーマー、ハードウェア機能を検証する必要があるプロフェッショナル、あるいは単にデバイスのグラフィック能力に興味がある方でも、Volume Shader BMは正確で信頼性の高い結果を提供します。</p>
                        <div class="mt-6">
                            <a href="#test" class="btn-primary inline-flex items-center px-4 py-2 rounded-md">
                                <i class="fas fa-play mr-2"></i> ベンチマークを開始
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-1 md:order-2">
                    <div class="rounded-lg overflow-hidden shadow-lg transform transition-transform hover:scale-105 duration-300">
                        <img src="../images/og-image.avif" alt="ボリュームシェーダーベンチマークの視覚化" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- GPU Architecture Evolution -->
        <div class="mb-8">
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-sitemap mr-2 text-indigo-500"></i>GPU アーキテクチャの進化
            </h3>
            <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                            <i class="fab fa-nvidia text-green-500 mr-2"></i>NVIDIA アーキテクチャ
                        </h4>
                        <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>Ada Lovelace</strong></span>
                                <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40 シリーズ</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>Ampere</strong></span>
                                <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30 シリーズ</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>Turing</strong></span>
                                <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20 シリーズ</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>Pascal</strong></span>
                                <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10 シリーズ</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                            <i class="fab fa-amd text-red-500 mr-2"></i>AMD アーキテクチャ
                        </h4>
                        <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>RDNA 3</strong></span>
                                <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000 シリーズ</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>RDNA 2</strong></span>
                                <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000 シリーズ</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>RDNA</strong></span>
                                <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000 シリーズ</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                <span><strong>GCN</strong></span>
                                <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500 シリーズ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Factors Affecting GPU Performance -->
        <div class="mb-8">
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-chart-area mr-2 text-pink-500"></i>GPUパフォーマンスに影響する要素
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                    <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">温度管理</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">過熱はスロットリングを引き起こし、パフォーマンスに影響します</p>
                </div>
                <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                    <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">電源供給</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">不十分な電力はGPUパフォーマンスを制限します</p>
                </div>
                <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                    <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">ドライバー</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">最新のドライバーはゲームパフォーマンスを最適化します</p>
                </div>
                <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                    <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">システム構成</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">CPUとRAMも全体的なパフォーマンスに影響します</p>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>GPU 推奨ガイド
            </h2>
            
            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>予算別おすすめ
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">エントリーレベル</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">1080p高品質ゲーミング</div>
                                <div class="text-xs text-green-600 dark:text-green-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">最高のコストパフォーマンス</div>
                                <div class="text-xs text-green-600 dark:text-green-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>適用用途:</strong> 1080pゲーミング、軽いコンテンツ制作、日常のオフィスワーク</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">ミドルレンジ</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">1440p高品質</div>
                                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">大容量VRAMの利点</div>
                                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>適用用途:</strong> 1440pゲーミング、動画編集、ライブストリーミング</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">ハイエンド</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">4Kゲーミングパワーハウス</div>
                                <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">24GB大容量VRAM</div>
                                <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>適用用途:</strong> 4Kゲーミング、プロフェッショナルレンダリング、AI計算</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">フラッグシップ</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">究極のパフォーマンス</div>
                                <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>適用用途:</strong> 4K/8K制作、AIトレーニング、科学計算、最高品質のゲーミング</p>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Usage-Based Recommendations -->
        <div class="mb-8">
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-tasks mr-2 text-blue-500"></i>用途別おすすめ
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Gaming GPUs -->
                <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                        <h4 class="font-semibold text-red-800 dark:text-red-200">ゲーミングGPU</h4>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">eスポーツゲーミング</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060, RX 7600</div>
                            <div class="text-xs text-red-600 dark:text-red-400">高FPS 1080p</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                    <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                </a>
                            </div>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">AAAタイトル</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7700 XT</div>
                            <div class="text-xs text-red-600 dark:text-red-400">1440p高品質</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                    <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                </a>
                            </div>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">4Kゲーミング</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                            <div class="text-xs text-red-600 dark:text-red-400">4K超高設定</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Content Creation -->
                <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                        <h4 class="font-semibold text-blue-800 dark:text-blue-200">コンテンツ制作</h4>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">動画編集</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7900 GRE</div>
                            <div class="text-xs text-blue-600 dark:text-blue-400">ハードウェアエンコード高速化</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                    <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                </a>
                            </div>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">3Dレンダリング</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                            <div class="text-xs text-blue-600 dark:text-blue-400">CUDA/OptiX高速化</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                </a>
                            </div>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">ライブストリーミング</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060以上</div>
                            <div class="text-xs text-blue-600 dark:text-blue-400">NVENCエンコード</div>
                            <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                <i class="fas fa-shopping-cart mr-1"></i>RTX 4060シリーズを見る
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Professional Work -->
                <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                        <h4 class="font-semibold text-purple-800 dark:text-purple-200">プロフェッショナルワーク</h4>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">AI開発</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                            <div class="text-xs text-purple-600 dark:text-purple-400">大容量VRAM要件</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                </a>
                            </div>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">CAD設計</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, Quadroシリーズ</div>
                            <div class="text-xs text-purple-600 dark:text-purple-400">プロフェッショナルドライバ最適化</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                    <i class="fas fa-shopping-cart mr-1"></i>Quadroシリーズ
                                </a>
                            </div>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                            <div class="font-medium text-gray-800 dark:text-gray-200">科学計算</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                            <div class="text-xs text-purple-600 dark:text-purple-400">倍精度浮動小数点演算</div>
                            <div class="flex gap-1 mt-2">
                                <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                    <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                </a>
                                <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                    <i class="fas fa-shopping-cart mr-1"></i>A5000
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Purchase Advice -->
        <div>
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>購入アドバイス
            </h3>
            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                            <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                            重要な考慮事項
                        </h4>
                        <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                            <li>• 十分な電源ワット数を確保する</li>
                            <li>• ケースのスペースと冷却を確認する</li>
                            <li>• CPUパフォーマンスとのマッチングを考慮する</li>
                            <li>• VRAM容量の要件に注意する</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                            <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                            購入に最適な時期
                        </h4>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• 新製品発売後3〜6ヶ月</li>
                            <li>• ブラックフライデーなどのショッピングイベント</li>
                            <li>• 暗号通貨マイニングの低迷後</li>
                            <li>• 中古市場の合理的な観察</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                            <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                            ブランド選択
                        </h4>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• ASUS ROG: ハイエンドゲーマー向けの最高の選択</li>
                            <li>• MSI Gaming: バランスの取れた価格性能比</li>
                            <li>• GIGABYTE AORUS: 優れた冷却性能</li>
                            <li>• Galax: エントリーレベルに適した価値</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Notes -->
        <div class="mt-6">
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                    <div>
                        <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">重要な注意事項</h4>
                        <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                            <li>• モバイルデバイスで高負荷テストを実行する場合は、バッテリー残量と発熱に注意してください</li>
                            <li>• テスト中に画面の乱れやシステムのフリーズが発生した場合は、すぐにテストを停止してください</li>
                            <li>• テスト結果は参考用です。実際のゲームパフォーマンスはCPU、メモリなど他の要因にも影響されます</li>
                            <li>• 定期的なテストはGPUパフォーマンスの変化を監視し、ハードウェアの問題を早期に特定するのに役立ちます</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Principles Area -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">技術原理</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>このツールは高度なWebGL 2.0技術とボリュームシェーダーアルゴリズムを使用して、以下の技術によりGPUパフォーマンステストを実装しています：</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>ボリュームレンダリング技術</strong>：複雑な数学関数を使用して3Dボリュームテクスチャを生成し、毒キノコの形状とテクスチャをシミュレート</li>
                    <li><strong>動的シェーダー</strong>：テストレベルに基づいてフラグメントシェーダーの複雑さを動的に調整し、GPU計算負荷を増加</li>
                    <li><strong>リアルタイムパフォーマンスモニタリング</strong>：WebGL拡張機能とパフォーマンスAPIを通じてFPSやレンダリング時間などの主要指標を監視</li>
                    <li><strong>温度推論アルゴリズム</strong>：パフォーマンス低下曲線からデバイスの温度状態を推測</li>
                </ul>
                <p>テストアルゴリズムはデバイスのパフォーマンス限界に達するまでレンダリングの複雑さを徐々に増加させ、GPUの処理能力を正確に評価します。</p>
            </div>
        </div>

        <!-- Compatibility Area -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">ブラウザ互換性</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">完全サポート。最高のパフォーマンスには最新バージョンを推奨。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">優れたパフォーマンスで完全サポート。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">基本サポート、一部の高度な機能は制限される場合があります。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">完全サポート、Chromeと同様のパフォーマンス。</p>
                </div>
            </div>
        </div>

        <!-- GPU Knowledge Base Area -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>GPU知識ベース
            </h2>
            
            <!-- GPU Basic Knowledge -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>GPU基礎知識
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">GPUとは</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">グラフィックス処理ユニット、グラフィックスレンダリングと並列計算に特化したチップで、多数の単純なタスクを同時に処理するのに優れた数千の小さなコアを持っています。</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPUとCPUの違い</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPUは少数の強力なコアを持ち、複雑な論理演算に適しています。GPUは多数の単純なコアを持ち、並列計算とグラフィックスレンダリングに最適です。最適なパフォーマンスのために連携して動作します。</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">GPUの種類</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">統合GPU：CPUに内蔵され、低消費電力だが性能は限定的。専用GPU：独立したGPUチップで、強力なパフォーマンスを発揮し、ゲームやプロフェッショナルな作業に適しています。</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Parameters Analysis -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>主要パラメータ分析
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">ビデオメモリ（VRAM）</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• グラフィックスデータを格納するための専用メモリ</li>
                                <li>• 容量は高解像度ゲームのパフォーマンスに影響</li>
                                <li>• 一般的なゲームには8GB以上を推奨</li>
                                <li>• 4Kゲームやプロフェッショナルな作業には12GB以上が必要</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">コアクロック</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• ベースクロック：GPUの安定動作周波数</li>
                                <li>• ブーストクロック：自動オーバークロック後の周波数</li>
                                <li>• 周波数が高いほど計算能力が強力</li>
                                <li>• オーバークロックによりさらに向上可能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">ストリームプロセッサ</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 並列計算を実行するコアユニット</li>
                                <li>• ユニット数が多いほど並列処理能力が強力</li>
                                <li>• NVIDIAではCUDAコアと呼ばれる</li>
                                <li>• AMDではStream Processors（SP）と呼ばれる</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">バス幅＆帯域幅</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• メモリバス幅はデータ転送に影響</li>
                                <li>• 256ビット以上はハイエンド構成</li>
                                <li>• 帯域幅 = バス幅 × メモリ周波数</li>
                                <li>• 高帯域幅はパフォーマンスのボトルネックを軽減</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU Architecture Evolution -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>GPU アーキテクチャの進化
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>NVIDIA アーキテクチャ
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40シリーズ</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30シリーズ</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20シリーズ</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10シリーズ</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>AMD アーキテクチャ
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000シリーズ</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000シリーズ</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000シリーズ</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500シリーズ</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Factors Affecting GPU Performance -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>GPUパフォーマンスに影響する要因
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">温度管理</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">過熱はスロットリングを引き起こし、パフォーマンスに影響します</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">電源供給</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">不十分な電力はGPUパフォーマンスを制限します</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">ドライバー</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">最新のドライバーはゲームパフォーマンスを最適化します</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">システム構成</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPUとRAMも全体的なパフォーマンスに影響します</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>GPU推奨ガイド
            </h2>
            
            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>予算別おすすめ
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">エントリーレベル</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">1080p高品質ゲーミング</div>
                                <div class="text-xs text-green-600 dark:text-green-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">最高のコストパフォーマンス</div>
                                <div class="text-xs text-green-600 dark:text-green-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>適用用途：</strong> 1080pゲーミング、軽いコンテンツ制作、日常のオフィスワーク</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">ミッドレンジ</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">1440p高品質</div>
                                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">大容量VRAMの利点</div>
                                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>適用用途：</strong> 1440pゲーミング、コンテンツ制作、マルチタスク</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">ハイエンド</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">4K高品質ゲーミング</div>
                                <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XT</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">コストパフォーマンスに優れた4Kゲーミング</div>
                                <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">おすすめ度 ⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xt">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>適用用途：</strong> 4Kゲーミング、プロフェッショナルなコンテンツ制作、ストリーミング</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-red-200 dark:border-red-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-red-800 dark:text-red-200 text-lg">フラッグシップ</h4>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">究極のパフォーマンス</div>
                                <div class="text-xs text-red-600 dark:text-red-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                            <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">AMDの最高峰モデル</div>
                                <div class="text-xs text-red-600 dark:text-red-400 mt-1">おすすめ度 ⭐⭐⭐⭐⭐</div>
                                <a href="#" class="purchase-btn mt-2 w-full bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                    <i class="fas fa-shopping-cart mr-1"></i>購入を見る
                                </a>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-red-700 dark:text-red-300">
                            <p><strong>適用用途：</strong> 4K/8K制作、AIトレーニング、プロフェッショナルなワークロード</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Professional Work -->
            <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                <div class="flex items-center mb-4">
                    <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                    <h4 class="font-semibold text-purple-800 dark:text-purple-200">プロフェッショナルワーク</h4>
                </div>
                <div class="space-y-3">
                    <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                        <div class="font-medium text-gray-800 dark:text-gray-200">AI開発</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                        <div class="text-xs text-purple-600 dark:text-purple-400">大容量VRAM要件</div>
                        <div class="flex gap-1 mt-2">
                            <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                            </a>
                            <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                            </a>
                        </div>
                    </div>
                    <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                        <div class="font-medium text-gray-800 dark:text-gray-200">CAD設計</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, Quadroシリーズ</div>
                        <div class="text-xs text-purple-600 dark:text-purple-400">プロフェッショナルドライバー最適化</div>
                        <div class="flex gap-1 mt-2">
                            <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                            </a>
                            <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                <i class="fas fa-shopping-cart mr-1"></i>Quadroシリーズ
                            </a>
                        </div>
                    </div>
                    <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                        <div class="font-medium text-gray-800 dark:text-gray-200">科学計算</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                        <div class="text-xs text-purple-600 dark:text-purple-400">倍精度浮動小数点演算</div>
                        <div class="flex gap-1 mt-2">
                            <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                            </a>
                            <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                <i class="fas fa-shopping-cart mr-1"></i>A5000
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Advice -->
        <div>
            <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>購入アドバイス
            </h3>
            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                            <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                            重要な考慮事項
                        </h4>
                        <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                            <li>• 十分な電源ワット数を確保する</li>
                            <li>• ケースのスペースと冷却を確認する</li>
                            <li>• CPUパフォーマンスとのマッチングを考慮する</li>
                            <li>• VRAM容量の要件に注意する</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                            <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                            購入に最適な時期
                        </h4>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• 新製品発売後3〜6ヶ月</li>
                            <li>• ブラックフライデーなどのショッピングイベント</li>
                            <li>• 暗号通貨マイニングの低迷後</li>
                            <li>• 中古市場の合理的な観察</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                            <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                            ブランド選択
                        </h4>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• ASUS ROG: ハイエンドゲーマー向けの最高の選択</li>
                            <li>• MSI Gaming: バランスの取れた価格とパフォーマンス</li>
                            <li>• GIGABYTE AORUS: 優れた冷却性能</li>
                            <li>• Galax: エントリーレベルに適した価値</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">よくある質問</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">テスト中にデバイスが熱くなるのはなぜですか？</h3>
                    <p class="text-gray-700 dark:text-gray-300">これは正常です。Volume Shader BM TestはGPUをフル稼働させるため、発熱は避けられません。温度が高すぎる場合は、テストを停止するか、テストレベルを下げることをお勧めします。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">テスト結果のスコアは何を表していますか？</h3>
                    <p class="text-gray-700 dark:text-gray-300">スコアはFPS、レンダリングの安定性、テストレベルを考慮しています。スコアが高いほどGPUのパフォーマンスが強力であることを示し、他のデバイスと比較するために使用できます。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">ブラウザがWebGLをサポートしていないと表示された場合はどうすればよいですか？</h3>
                    <p class="text-gray-700 dark:text-gray-300">ブラウザが最新であることを確認し、ハードウェアアクセラレーションが有効になっているか確認するか、別のブラウザを試してください。一部のモバイルデバイスはWebGL 2.0をサポートしていない場合があります。</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">テストデータはアップロードされますか？</h3>
                    <p class="text-gray-700 dark:text-gray-300">すべてのテストデータはローカルに保存され、サーバーにアップロードされることはありません。プライバシーの問題を心配せずに安全にテストを実行できます。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">プロフェッショナルオンラインGPUパフォーマンステストツール</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">Volume Shader BM Test</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">関連リンク</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">ユーザーガイド</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">テクニカルサポート</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">フィードバック</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">免責事項</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">このツールはGPUパフォーマンステスト専用です。使用中のデバイスの発熱や電力消費は正常な現象です。デバイスの性能に基づいて適切なテストレベルを選択してください。</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../static/js/app.js"></script>
    
    <!-- 言語切り替えスクリプト -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            // 言語ドロップダウンメニューの表示/非表示を切り替え
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });
            
            // ページの他の場所をクリックするとドロップダウンメニューを閉じる
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>