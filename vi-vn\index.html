<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM Test Website - Công cụ đánh giá hiệu năng GPU trực tuyến</title>
    <meta name="description" content="Công cụ kiểm tra hiệu năng GPU trực tuyến chuyên nghiệ<PERSON>, đ<PERSON>h gi<PERSON> hiệu năng card đồ họa thông qua kết xuất 3D Volume Shader phức tạp, hỗ trợ giám sát FPS thời gian thực và phân tích hiệu năng.">
    <meta name="keywords" content="kiểm tra card đồ họa,kiểm tra GPU,Toxic Mushroom Test,volumeshader,WebGL,kiểm tra FPS">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/vi-vn/">
    <link rel="icon" href="../logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="../logo.svg">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/vi-vn/">
    <meta property="og:title" content="Volume Shader BM Test | Công cụ đánh giá hiệu năng GPU trực tuyến">
    <meta property="og:description" content="Công cụ kiểm tra hiệu năng GPU trực tuyến chuyên nghiệp, đánh giá hiệu năng card đồ họa thông qua kết xuất 3D Volume Shader phức tạp, hỗ trợ giám sát FPS thời gian thực và phân tích hiệu năng.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.avif">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/vi-vn/">
    <meta property="twitter:title" content="Volume Shader BM Test | Công cụ đánh giá hiệu năng GPU trực tuyến">
    <meta property="twitter:description" content="Công cụ kiểm tra hiệu năng GPU trực tuyến chuyên nghiệp, đánh giá hiệu năng card đồ họa thông qua kết xuất 3D Volume Shader phức tạp, hỗ trợ giám sát FPS thời gian thực và phân tích hiệu năng.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/og-img.avif">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/#website",
                "url": "https://volumeshaderbmtest.com/vi-vn/",
                "name": "Volume Shader BM Test",
                "description": "Công cụ đánh giá hiệu năng GPU trực tuyến chuyên nghiệp",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/#organization",
                "name": "Toxic Mushroom Test Team",
                "url": "https://volumeshaderbmtest.com/vi-vn/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/#software",
                "name": "Volume Shader BM Test",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/vi-vn/",
                "description": "Công cụ đánh giá hiệu năng GPU trực tuyến dựa trên WebGL, hỗ trợ bốn chế độ kiểm tra: nhẹ, trung bình, nặng và cực đoan, với giám sát thời gian thực FPS, nhiệt độ và các chỉ số hiệu năng quan trọng khác.",
                "featureList": [
                    "Kiểm tra hiệu năng GPU",
                    "Giám sát FPS thời gian thực",
                    "Giám sát trạng thái nhiệt độ",
                    "Ghi lại lịch sử hiệu năng",
                    "Phân tích kết quả kiểm tra",
                    "Kết xuất thể tích WebGL",
                    "Nhiều chế độ kiểm tra độ khó"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "CNY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/#gpu-knowledge",
                "headline": "Cơ sở dữ liệu kiến thức GPU - Hướng dẫn kỹ thuật toàn diện về card đồ họa",
        "description": "Giới thiệu chi tiết về kiến thức cơ bản GPU, các thông số chính, phát triển kiến trúc và các yếu tố ảnh hưởng đến hiệu năng, cung cấp hướng dẫn chuyên nghiệp cho việc lựa chọn card đồ họa và tối ưu hóa hiệu năng.",
                "author": {
                    "@type": "Organization",
                    "name": "Toxic Mushroom Test Team"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "Tại sao thiết bị của tôi nóng lên trong quá trình kiểm tra?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Điều này là bình thường. Bài kiểm tra Toxic Mushroom khiến GPU hoạt động với công suất tối đa, và việc tạo ra nhiệt là không thể tránh khỏi. Nếu nhiệt độ quá cao, khuyến nghị dừng kiểm tra hoặc giảm mức độ kiểm tra."
        }
                    },
                    {
                        "@type": "Question",
                        "name": "Điểm số trong kết quả kiểm tra có nghĩa là gì?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Điểm số tính đến FPS, độ ổn định kết xuất và mức độ kiểm tra. Điểm số cao hơn thể hiện hiệu năng GPU mạnh hơn và có thể so sánh ngang bằng với các thiết bị khác."
        }
                    },
                    {
                        "@type": "Question",
                        "name": "Phải làm gì khi trình duyệt hiển thị không hỗ trợ WebGL?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Vui lòng đảm bảo trình duyệt của bạn được cập nhật mới nhất, kiểm tra xem tăng tốc phần cứng có được bật hay không, hoặc thử chuyển đổi trình duyệt. Một số thiết bị di động có thể không hỗ trợ WebGL 2.0."
        }
                    },
                    {
                        "@type": "Question",
                        "name": "Dữ liệu kiểm tra có được tải lên không?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Tất cả dữ liệu kiểm tra được lưu trữ cục bộ và không được tải lên máy chủ. Bạn có thể thực hiện kiểm tra một cách an toàn mà không cần lo lắng về vấn đề bảo mật dữ liệu."
        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/#gpu-recommendations",
                "name": "Hướng dẫn khuyến nghị card đồ họa",
                "description": "Khuyến nghị card đồ họa chuyên nghiệp được phân loại theo ngân sách và mục đích sử dụng",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
          "description": "Card đồ họa gaming cấp độ nhập môn, phù hợp cho gaming 1080p chất lượng cao",
                        "category": "Card đồ họa",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1500",
                            "highPrice": "2000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
          "description": "Lựa chọn tỷ lệ giá/hiệu năng tốt nhất, card đồ họa nhập môn được khuyến nghị",
                        "category": "Card đồ họa",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1400",
                            "highPrice": "1800",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
          "description": "Card đồ họa gaming tầm trung, hỗ trợ gaming 1440p chất lượng cao",
                        "category": "Card đồ họa",
                         "brand": {
                             "@type": "Brand",
                             "name": "NVIDIA"
                         },
                         "offers": {
                             "@type": "Offer",
                             "price": "599",
                             "priceCurrency": "USD",
                             "availability": "https://schema.org/InStock"
                         }
                     },
                     {
                         "@type": "Product",
                         "position": 4,
                         "name": "NVIDIA GeForce RTX 4090",
                         "description": "Card đồ họa hàng đầu, hiệu năng gaming 4K tối ưu",
                         "category": "Card đồ họa",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "12000",
                            "highPrice": "15000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/#test-guide",
                "name": "Hướng dẫn kiểm tra hiệu năng GPU",
                "description": "Các bước kiểm tra hiệu năng GPU chi tiết và thực hành tốt nhất",
                "image": "https://volumeshaderbmtest.com/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "Trình duyệt tương thích WebGL"
                    },
                    {
                        "@type": "HowToSupply", 
                        "name": "Card đồ họa rời hoặc tích hợp"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "Công cụ kiểm tra Volume Shader BM"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "Chuẩn bị trước khi kiểm tra",
          "text": "Đóng các chương trình và tab trình duyệt không cần thiết, đảm bảo môi trường tản nhiệt tốt cho thiết bị"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "Chọn chế độ kiểm tra",
          "text": "Chọn chế độ kiểm tra phù hợp theo hiệu năng thiết bị, khuyến nghị bắt đầu với chế độ nhẹ"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "Bắt đầu kiểm tra",
          "text": "Nhấp vào bắt đầu kiểm tra và quan sát các thay đổi dữ liệu hiệu năng thời gian thực"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "Hiển thị kết quả",
          "text": "Xem báo cáo chi tiết và khuyến nghị hiệu năng sau khi hoàn thành kiểm tra"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="../static/js/js.js"></script>
    <script src="../static/js/chart.js"></script>
    <script src="../static/js/three.min.js"></script>
    <link rel="stylesheet" href="../static/css/all.min.css">
    <link href="../static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="../static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="../logo.svg" alt="Logo" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader BM Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="#test" class="active nav-link">Kiểm tra hiệu năng</a></li>
                    <li><a href="#about" class="nav-link">Giới thiệu công cụ</a></li>
                    <li><a href="#test-guide" class="nav-link">Hướng dẫn kiểm tra</a></li>
                    <li><a href="#technology" class="nav-link">Nguyên lý kỹ thuật</a></li>
                    <!-- <li><a href="#compatibility" class="nav-link">Tương thích</a></li>
                    <li><a href="#gpu-knowledge" class="nav-link">Kiến thức GPU</a></li>
                    <li><a href="#hardware-recommendation" class="nav-link">Khuyến nghị phần cứng</a></li> -->
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="Chuyển đổi ngôn ngữ">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">Tiếng Việt</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">English</a>
                        <a href="../zh-cn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="../ja-jp/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="Chuyển đổi chủ đề">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="Menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Kiểm tra hiệu năng</a></li>
                <li><a href="#about" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Giới thiệu công cụ</a></li>
                <li><a href="#test-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Hướng dẫn kiểm tra</a></li>
                <li><a href="#technology" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Nguyên lý kỹ thuật</a></li>
                <li><a href="#compatibility" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Tương thích</a></li>
                <li><a href="#gpu-knowledge" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Kiến thức GPU</a></li> -->
                <li><a href="#hardware-recommendation" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Khuyến nghị phần cứng</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
          
        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader BM Test</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> Toàn màn hình
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> Chụp màn hình
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">Nhấp vào nút Bắt đầu kiểm tra để bắt đầu kết xuất</div>
                                <div class="lg:hidden">Nhấp vào nút bên dưới để bắt đầu kiểm tra GPU</div>
                                <div class="text-sm text-gray-300 mt-1">Đảm bảo thiết bị của bạn hỗ trợ WebGL</div>
                            </div>
                        </div>
                        
                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                
                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">Chọn chế độ kiểm tra</h3>
                                    </div>
                                    
                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Kiểm tra nhẹ</h4>
                                                        <p class="text-gray-300 text-sm">Kiểm tra hiệu năng cơ bản, phù hợp cho thiết bị hiệu năng thấp</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Kiểm tra trung bình</h4>
                                                        <p class="text-gray-300 text-sm">Kiểm tra tải tiêu chuẩn, phù hợp cho thiết bị tầm trung</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Kiểm tra nặng</h4>
                                                        <p class="text-gray-300 text-sm">Kiểm tra cường độ cao, phù hợp cho thiết bị cao cấp</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Kiểm tra cực đoan</h4>
                                                        <p class="text-gray-300 text-sm">Kiểm tra căng thẳng cực đoan, sử dụng cẩn thận</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            Bắt đầu kiểm tra
                                        </button>
                                        
                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            Dừng kiểm tra
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                Bắt đầu kiểm tra GPU
                            </button>
                        </div>
                        
                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>Thời gian kết xuất: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>Tam giác: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>Độ phức tạp: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>
                        
                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">Phát hiện vấn đề hiệu năng, hãy xem xét giảm mức độ kiểm tra</span>
                        </div>
                    </div>
                    
                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Thông tin thiết bị</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">Đang phát hiện...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Phiên bản WebGL:</span>
                                <span id="webgl-version" class="text-right">Đang phát hiện...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Trình duyệt:</span>
                                <span id="browser-info" class="text-right">Đang phát hiện...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Hệ điều hành:</span>
                                <span id="os-info" class="text-right">Đang phát hiện...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Kiểm tra nhẹ</h3>
                                    <p>Kiểm tra hiệu năng cơ bản</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Kiểm tra trung bình</h3>
                                    <p>Kiểm tra tải tiêu chuẩn</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Kiểm tra nặng</h3>
                                    <p>Kiểm tra cường độ cao</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Kiểm tra cực đoan</h3>
                                    <p>Kiểm tra căng thẳng cực đoan</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Tùy chỉnh</h3>
                                    <p>Kiểm tra tham số tùy chỉnh</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Điều khiển kiểm tra</h3>
                        
                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> Bắt đầu kiểm tra
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> Dừng kiểm tra
                            </button>
                        </div>
                        
                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>Tiến độ kiểm tra</span>
                                <span id="progress-text">0/60 giây</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Số lượng tam giác</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hiện tại: <span id="triangle-value">50.000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Độ phức tạp Shader</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hiện tại: <span id="complexity-value">2,5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Thời gian kiểm tra (giây)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hiện tại: <span id="duration-value">60</span> giây</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Hiệu năng thời gian thực</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">Điểm số</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">Bình thường</div>
                                <div class="text-gray-500 dark:text-gray-400">Trạng thái nhiệt độ</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">Nhẹ</div>
                                <div class="text-gray-500 dark:text-gray-400">Cấp độ kiểm tra</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> Chia sẻ kết quả
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> Lịch sử kiểm tra
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Phân tích hiệu năng</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">Biểu đồ FPS</button>
                        <button class="chart-tab" data-chart="score">Xu hướng điểm số</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">FPS trung bình</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">FPS tối đa</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">FPS tối thiểu</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Độ ổn định</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Lịch sử kiểm tra</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> Xóa
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>Chưa có bản ghi kiểm tra nào</p>
                        <p class="text-sm">Các bài kiểm tra hiệu năng đã hoàn thành sẽ được hiển thị ở đây</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
Tổng cộng <span id="history-count">0</span> bài kiểm tra
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> Xuất
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Shader BM Introduction Section -->
        <div class="card p-6 mt-6 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                <div class="order-2 md:order-1">
                    <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Khám phá Volume Shader BM</h2>
                    <div class="text-gray-700 dark:text-gray-300 space-y-4">
                        <p>Volume Shader BM là một công cụ benchmark GPU tiên tiến được thiết kế để đẩy phần cứng đồ họa của bạn đến giới hạn thông qua việc render shader thể tích phức tạp.</p>
                        <p>Benchmark của chúng tôi sử dụng các shader thể tích 3D tiên tiến để tạo ra các mẫu fractal thực tế, thử thách khả năng xử lý của GPU và cung cấp các chỉ số hiệu năng chính xác.</p>
                        <p>Dù bạn là một game thủ muốn đánh giá hiệu năng hệ thống, một chuyên gia cần xác thực khả năng phần cứng, hay đơn giản là tò mò về khả năng đồ họa của thiết bị, Volume Shader BM đều cung cấp kết quả chính xác và đáng tin cậy.</p>
                        <div class="mt-6">
                            <a href="#test" class="btn-primary inline-flex items-center px-4 py-2 rounded-md">
                                <i class="fas fa-play mr-2"></i> Bắt đầu Benchmark
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-1 md:order-2">
                    <div class="rounded-lg overflow-hidden shadow-lg transform transition-transform hover:scale-105 duration-300">
                        <img src="../images/og-image.avif" alt="Volume Shader Benchmark Visualization" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- About Tool Area -->
        <div id="about" class="card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Giới thiệu Volume Shader BM Test</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Volume Shader BM Test là một công cụ đánh giá hiệu năng GPU trực tuyến dựa trên WebGL, đánh giá hiệu năng đồ họa của thiết bị thông qua việc render các shader thể tích 3D phức tạp.</p>
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2">Mô tả các cấp độ kiểm tra</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm">Nhẹ: 10K tam giác</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm">Trung bình: 50K tam giác</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                            <span class="text-sm">Nặng: 200K tam giác</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm">Cực độ: 500K tam giác</span>
                        </div>
                    </div>
                </div>
                <p><strong>Lưu ý:</strong> Các bài kiểm tra cường độ cao có thể gây ra độ trễ hoặc nóng lên trên các thiết bị hiệu năng thấp. Vui lòng chọn cấp độ kiểm tra phù hợp dựa trên hiệu năng thiết bị của bạn.</p>
            </div>
        </div>

        <!-- Test Guide Area -->
        <div id="test-guide" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-book-open mr-2 text-primary-500"></i>Hướng dẫn kiểm tra GPU
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Preparation -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-clipboard-check mr-2 text-green-500"></i>Chuẩn bị kiểm tra
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Đóng các chương trình và tab trình duyệt không cần thiết để giải phóng tài nguyên hệ thống</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Đảm bảo thiết bị có điều kiện tản nhiệt tốt để tránh quá nhiệt trong quá trình kiểm tra</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Sử dụng nguồn điện ổn định để tránh gián đoạn trong quá trình kiểm tra</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Cập nhật driver đồ họa lên phiên bản mới nhất để có hiệu năng tối ưu</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Test Steps -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-list-ol mr-2 text-blue-500"></i>Các bước kiểm tra
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                <span>Chọn chế độ kiểm tra phù hợp (khuyến nghị bắt đầu với mức Nhẹ)</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                <span>Điều chỉnh độ phức tạp và thời gian kiểm tra nếu cần</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                <span>Nhấp vào Bắt đầu kiểm tra và quan sát dữ liệu hiệu năng thời gian thực</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                                <span>Xem báo cáo chi tiết và khuyến nghị sau khi hoàn thành kiểm tra</span>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- Test Mode Details -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-layer-group mr-2 text-purple-500"></i>Chi tiết các chế độ kiểm tra
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                            <h4 class="font-medium text-green-800 dark:text-green-200">Kiểm tra nhẹ</h4>
                        </div>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• Phù hợp cho thiết bị văn phòng và GPU cơ bản</li>
                            <li>• 10K tam giác, tải GPU thấp</li>
                            <li>• Thời gian kiểm tra: 30 giây</li>
                            <li>• Ít tỏa nhiệt, phù hợp cho chạy dài</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">Kiểm tra trung bình</h4>
                        </div>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Phù hợp cho GPU gaming tầm trung</li>
                            <li>• 50K tam giác, tải GPU trung bình</li>
                            <li>• Thời gian kiểm tra: 60 giây</li>
                            <li>• Có thể phát hiện nghẽn cổ chai và độ ổn định</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200">Kiểm tra nặng</h4>
                        </div>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• Phù hợp cho GPU gaming cao cấp</li>
                            <li>• 200K tam giác, tải GPU cao</li>
                            <li>• Thời gian kiểm tra: 90 giây</li>
                            <li>• Kiểm tra hệ thống tản nhiệt và độ ổn định nguồn</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                            <h4 class="font-medium text-red-800 dark:text-red-200">Kiểm tra cực độ</h4>
                        </div>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• Phù hợp cho GPU chuyên nghiệp cao cấp</li>
                            <li>• 500K tam giác, tải GPU cực độ</li>
                            <li>• Thời gian kiểm tra: 120 giây</li>
                            <li>• Stress test, có thể gây nóng thiết bị</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Results Interpretation Guide -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-indigo-500"></i>Hướng dẫn giải thích kết quả
                </h3>
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">Phân tích FPS</h4>
                            <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                                <li>• <strong>60+ FPS</strong>: Hiệu năng xuất sắc</li>
                                <li>• <strong>30-60 FPS</strong>: Hiệu năng tốt</li>
                                <li>• <strong>15-30 FPS</strong>: Hiệu năng trung bình</li>
                                <li>• <strong>&lt;15 FPS</strong>: Hiệu năng thấp</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">Trạng thái nhiệt độ</h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• <strong>Bình thường</strong>: Nhiệt độ GPU ổn định</li>
                                <li>• <strong>Ấm</strong>: Nhiệt độ tăng nhẹ, trong phạm vi bình thường</li>
                                <li>• <strong>Nóng</strong>: Cần tản nhiệt</li>
                                <li>• <strong>Quá nhiệt</strong>: Khuyến nghị dừng kiểm tra</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">Tổng điểm</h4>
                            <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                                <li>• <strong>9000+</strong>: Hiệu năng hàng đầu</li>
                                <li>• <strong>6000-9000</strong>: Hiệu năng cao cấp</li>
                                <li>• <strong>3000-6000</strong>: Hiệu năng tầm trung</li>
                                <li>• <strong>&lt;3000</strong>: Hiệu năng cơ bản</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Important Notes -->
            <div class="mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Lưu ý quan trọng</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Chú ý mức pin và nhiệt độ khi thực hiện kiểm tra cường độ cao trên thiết bị di động</li>
                                <li>• Nếu xuất hiện lỗi hiển thị hoặc hệ thống treo trong quá trình kiểm tra, hãy dừng ngay lập tức</li>
                                <li>• Kết quả kiểm tra chỉ mang tính tham khảo; hiệu năng game thực tế còn bị ảnh hưởng bởi CPU, RAM và các yếu tố khác</li>
                                <li>• Kiểm tra định kỳ có thể giúp theo dõi thay đổi hiệu năng GPU và phát hiện sớm vấn đề phần cứng</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Principles Area -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Nguyên lý kỹ thuật</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Công cụ này sử dụng công nghệ WebGL 2.0 tiên tiến và thuật toán shader thể tích để thực hiện kiểm tra hiệu năng GPU thông qua các kỹ thuật sau:</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>Công nghệ Render thể tích</strong>: Sử dụng các hàm toán học phức tạp để tạo ra texture thể tích 3D, mô phỏng hình dạng và kết cấu của nấm độc</li>
                    <li><strong>Shader động</strong>: Điều chỉnh độ phức tạp của fragment shader một cách động dựa trên cấp độ kiểm tra và tăng tải tính toán GPU</li>
                    <li><strong>Giám sát hiệu năng thời gian thực</strong>: Theo dõi các chỉ số quan trọng như FPS và thời gian render thông qua WebGL extensions và Performance API</li>
                    <li><strong>Thuật toán suy luận nhiệt độ</strong>: Suy luận trạng thái nhiệt độ thiết bị thông qua đường cong suy giảm hiệu năng</li>
                </ul>
                <p>Thuật toán kiểm tra tăng dần độ phức tạp render cho đến khi đạt giới hạn hiệu năng của thiết bị, từ đó đánh giá chính xác khả năng xử lý của GPU.</p>
            </div>
        </div>

        <!-- Compatibility Area -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Tương thích trình duyệt</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Hỗ trợ đầy đủ. Khuyến nghị phiên bản mới nhất để có hiệu năng tốt nhất.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Hỗ trợ đầy đủ với hiệu năng xuất sắc.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Hỗ trợ cơ bản, một số tính năng nâng cao có thể bị hạn chế.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Hỗ trợ đầy đủ, hiệu năng tương tự Chrome.</p>
                </div>
            </div>
        </div>

        <!-- GPU Knowledge Base Area -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>Cơ sở tri thức GPU
            </h2>
            
            <!-- GPU Basic Knowledge -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>Kiến thức cơ bản về GPU
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">GPU là gì</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">Graphics Processing Unit (Đơn vị xử lý đồ họa), một chip chuyên xử lý render đồ họa và tính toán song song, với hàng nghìn lõi nhỏ xuất sắc trong việc xử lý nhiều tác vụ đơn giản cùng lúc.</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPU vs CPU</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPU có ít lõi nhưng mạnh hơn, phù hợp cho các phép toán logic phức tạp; GPU có nhiều lõi đơn giản, lý tưởng cho tính toán song song và render đồ họa. Chúng hoạt động cùng nhau để có hiệu năng tối ưu.</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">Loại GPU</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">GPU tích hợp: Được tích hợp vào CPU, tiêu thụ điện năng thấp nhưng hiệu năng hạn chế; GPU rời: Chip GPU riêng biệt, hiệu năng mạnh mẽ, phù hợp cho gaming và công việc chuyên nghiệp.</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Parameters Analysis -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>Phân tích các thông số chính
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Bộ nhớ video (VRAM)</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Bộ nhớ chuyên dụng để lưu trữ dữ liệu đồ họa</li>
                                <li>• Dung lượng ảnh hưởng đến hiệu năng gaming ở độ phân giải cao</li>
                                <li>• Khuyến nghị 8GB+ cho các game phổ thông</li>
                                <li>• Cần 12GB+ cho gaming 4K hoặc công việc chuyên nghiệp</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Xung nhịp lõi</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Xung nhịp cơ bản: Tần số hoạt động ổn định của GPU</li>
                                <li>• Xung nhịp boost: Tần số sau khi tự động ép xung</li>
                                <li>• Tần số cao hơn có nghĩa là sức mạnh tính toán mạnh hơn</li>
                                <li>• Có thể cải thiện thêm thông qua ép xung</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Bộ xử lý luồng</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Đơn vị lõi thực hiện tính toán song song</li>
                                <li>• Nhiều đơn vị hơn có nghĩa là khả năng xử lý song song mạnh hơn</li>
                                <li>• Ở NVIDIA được gọi là lõi CUDA</li>
                                <li>• Ở AMD được gọi là bộ xử lý luồng (SP)</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Độ rộng bus & Băng thông</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Độ rộng bus bộ nhớ ảnh hưởng đến truyền dữ liệu</li>
                                <li>• 256-Bit trở lên là cấu hình cao cấp</li>
                                <li>• Băng thông = Độ rộng bus × Tần số bộ nhớ</li>
                                <li>• Băng thông cao giảm thiểu nút thắt hiệu năng</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU Architecture Evolution -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>Sự phát triển kiến trúc GPU
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>Kiến trúc NVIDIA
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10 Serie</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>Kiến trúc AMD
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500 Series</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Factors Affecting GPU Performance -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>Các yếu tố ảnh hưởng đến hiệu năng GPU
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Kiểm soát nhiệt độ</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Quá nhiệt gây ra hiện tượng giảm tốc và ảnh hưởng đến hiệu năng</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Nguồn điện</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Nguồn điện không đủ sẽ hạn chế hiệu năng GPU</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Driver</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Driver mới nhất tối ưu hóa hiệu năng gaming</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Cấu hình hệ thống</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPU và RAM cũng ảnh hưởng đến hiệu năng tổng thể</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>Hướng dẫn khuyến nghị GPU
            </h2>
            
            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>Khuyến nghị theo ngân sách
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">Cấp độ nhập môn</h4>
                            <!-- <p class="text-sm text-green-600 dark:text-green-400">￥1000-2000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Gaming 1080p chất lượng cao</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Khuyến nghị ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Lựa chọn tốt nhất về giá/hiệu năng</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Khuyến nghị ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>Phù hợp cho:</strong> Gaming 1080p, tạo nội dung nhẹ, công việc văn phòng hàng ngày</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">Tầm trung</h4>
                            <!-- <p class="text-sm text-blue-600 dark:text-blue-400">￥2000-4000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1440p chất lượng cao</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Khuyến nghị ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Ưu thế VRAM lớn</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Khuyến nghị ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>Phù hợp cho:</strong> Gaming 1440p, chỉnh sửa video, live streaming</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">Cao cấp</h4>
                            <!-- <p class="text-sm text-purple-600 dark:text-purple-400">￥4000-8000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Sức mạnh gaming 4K</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Khuyến nghị ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">VRAM lớn 24GB</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Khuyến nghị ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>Phù hợp cho:</strong> Gaming 4K, render chuyên nghiệp, tính toán AI</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">Hàng đầu</h4>
                            <!-- <p class="text-sm text-orange-600 dark:text-orange-400">￥8000+</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Hiệu năng tối thượng</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Khuyến nghị ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080 Super</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Hàng đầu giá hợp lý hơn</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Khuyến nghị ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080-super">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem mua hàng
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>Phù hợp cho:</strong> Gaming 8K, workstation, người đam mê</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Usage-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-tasks mr-2 text-blue-500"></i>Khuyến nghị theo mục đích sử dụng
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Gaming GPUs -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                            <h4 class="font-semibold text-red-800 dark:text-red-200">GPU Gaming</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Gaming Esports</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060, RX 7600</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">FPS cao 1080p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Game AAA</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7700 XT</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">1440p chất lượng cao</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Gaming 4K</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">Cài đặt 4K Ultra</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Content Creation -->
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">Tạo nội dung</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Chỉnh sửa video</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7900 GRE</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Tăng tốc mã hóa phần cứng</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Render 3D</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Tăng tốc CUDA/OptiX</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Live Streaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060 trở lên</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Mã hóa NVENC</div>
                                 <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                     <i class="fas fa-shopping-cart mr-1"></i>Xem dòng RTX 4060
                                 </a>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Professional Work -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200">Công việc chuyên nghiệp</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Phát triển AI</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Nhu cầu VRAM cao</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Thiết kế CAD</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, dòng Quadro</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Tối ưu hóa driver chuyên nghiệp</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                         <i class="fas fa-shopping-cart mr-1"></i>Dòng Quadro
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Tính toán khoa học</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Số thực dấu phẩy động độ chính xác kép</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                         <i class="fas fa-shopping-cart mr-1"></i>A5000
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
            </div>
            
            <!-- Purchase Advice -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>Tư vấn mua hàng
                </h3>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                                Những cân nhắc quan trọng
                            </h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Đảm bảo nguồn điện đủ mạnh</li>
                                <li>• Kiểm tra không gian case và hệ thống tản nhiệt</li>
                                <li>• Xem xét sự cân bằng hiệu năng CPU</li>
                                <li>• Chú ý đến yêu cầu dung lượng VRAM</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                                <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                                Thời điểm mua tốt nhất
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• 3-6 tháng sau khi sản phẩm ra mắt</li>
                                <li>• Black Friday và các sự kiện mua sắm khác</li>
                                <li>• Sau khi thị trường đào tiền ảo suy giảm</li>
                                <li>• Theo dõi hợp lý thị trường đồ cũ</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                                Lựa chọn thương hiệu
                            </h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                                <li>• ASUS ROG: Lựa chọn hàng đầu cho game thủ cao cấp</li>
                                <li>• MSI Gaming: Tỷ lệ giá/hiệu năng cân bằng</li>
                                <li>• GIGABYTE AORUS: Hệ thống tản nhiệt xuất sắc</li>
                                <li>• Galax: Giá trị tốt cho người mới bắt đầu</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Câu hỏi thường gặp</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Tại sao thiết bị của tôi nóng lên trong quá trình kiểm tra?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Điều này là bình thường. Bài kiểm tra Toxic Mushroom khiến GPU của bạn hoạt động với công suất tối đa, do đó việc tạo ra nhiệt là không thể tránh khỏi. Nếu nhiệt độ quá cao, chúng tôi khuyên bạn nên dừng kiểm tra hoặc giảm mức độ kiểm tra.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Điểm số trong kết quả kiểm tra có nghĩa là gì?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Điểm số tính đến FPS, độ ổn định kết xuất và mức độ kiểm tra. Điểm số cao hơn cho thấy hiệu năng GPU mạnh hơn và có thể được sử dụng để so sánh trực tiếp với các thiết bị khác.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Phải làm gì khi trình duyệt hiển thị không hỗ trợ WebGL?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Vui lòng đảm bảo trình duyệt của bạn được cập nhật mới nhất, kiểm tra xem tăng tốc phần cứng có được bật hay không, hoặc thử một trình duyệt khác. Một số thiết bị di động có thể không hỗ trợ WebGL 2.0.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Dữ liệu kiểm tra của tôi có được tải lên không?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Tất cả dữ liệu kiểm tra được lưu trữ cục bộ và không được tải lên máy chủ. Bạn có thể thực hiện kiểm tra một cách an toàn mà không cần lo lắng về vấn đề bảo mật dữ liệu.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">Công cụ kiểm tra hiệu năng GPU trực tuyến chuyên nghiệp</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">Volume Shader BM Test</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Liên kết liên quan</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Hướng dẫn sử dụng</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Hỗ trợ kỹ thuật</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Phản hồi</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Tuyên bố miễn trừ trách nhiệm</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Công cụ này chỉ dành cho việc kiểm tra hiệu năng GPU. Việc thiết bị nóng lên và tiêu thụ điện năng trong quá trình sử dụng là hiện tượng bình thường. Vui lòng chọn mức độ kiểm tra phù hợp dựa trên khả năng của thiết bị.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../static/js/app.js"></script>
    
    <!-- Script chọn ngôn ngữ -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            // Hiển thị/ẩn menu chọn ngôn ngữ
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });
            
            // Đóng menu dropdown khi click vào nơi khác trên trang
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>