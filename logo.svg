<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#991b1b;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#dc2626;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#15803d;stop-opacity:1" />
        </linearGradient>
        <radialGradient id="grad2" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:#991b1b;stop-opacity:0.9" />
            <stop offset="50%" style="stop-color:#dc2626;stop-opacity:0.7" />
            <stop offset="100%" style="stop-color:#15803d;stop-opacity:0.5" />
        </radialGradient>
    </defs>
    
    <!-- 背景圆形 -->
    <circle cx="512" cy="512" r="512" fill="url(#grad2)" opacity="0.2"/>
    
    <!-- 主圆形 -->
    <circle cx="512" cy="512" r="400" fill="url(#grad1)" opacity="0.5"/>
    
    <!-- 内层圆形 -->
    <circle cx="512" cy="512" r="300" fill="none" stroke="#991b1b" stroke-width="8" opacity="0.8"/>
    <circle cx="512" cy="512" r="250" fill="none" stroke="#15803d" stroke-width="6" opacity="0.9"/>
    <circle cx="512" cy="512" r="200" fill="none" stroke="#991b1b" stroke-width="4" opacity="1"/>
    
    <!-- 装饰性小圆点 -->
    <circle cx="512" cy="312" r="15" fill="#991b1b" opacity="1"/>
    <circle cx="512" cy="712" r="15" fill="#15803d" opacity="1"/>
    <circle cx="312" cy="512" r="15" fill="#991b1b" opacity="1"/>
    <circle cx="712" cy="512" r="15" fill="#15803d" opacity="1"/>
    
    <!-- 对角线装饰 -->
    <g stroke="#ffffff" stroke-width="4" fill="none" opacity="0.6">
        <path d="M312,312 L712,712"/>
        <path d="M712,312 L312,712"/>
    </g>
    
    <!-- 中心装饰 -->
    <circle cx="512" cy="512" r="80" fill="url(#grad1)" opacity="0.7"/>
    <circle cx="512" cy="512" r="60" fill="#ffffff" opacity="0.5"/>
    <circle cx="512" cy="512" r="40" fill="url(#grad1)" opacity="0.9"/>
</svg> 