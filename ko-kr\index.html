<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM Test Website - 온라인 GPU 성능 벤치마크 도구</title>
    <meta name="description" content="복잡한 3D 볼륨 셰이더 렌더링을 통해 그래픽 카드 성능을 테스트하는 전문 온라인 GPU 성능 테스트 도구로, 실시간 FPS 모니터링 및 성능 분석을 지원합니다.">
    <meta name="keywords" content="그래픽카드 테스트,GPU 테스트,Toxic Mushroom Test,volumeshader,WebGL,FPS 테스트">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/ko-kr/">
    <link rel="icon" href="../logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="../logo.svg">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/ko-kr/">
    <meta property="og:title" content="Volume Shader BM Test | 온라인 GPU 성능 벤치마크 도구">
    <meta property="og:description" content="복잡한 3D 볼륨 셰이더 렌더링을 통해 그래픽 카드 성능을 테스트하는 전문 온라인 GPU 성능 테스트 도구로, 실시간 FPS 모니터링 및 성능 분석을 지원합니다.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.avif">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/ko-kr/">
    <meta property="twitter:title" content="Volume Shader BM Test | 온라인 GPU 성능 벤치마크 도구">
    <meta property="twitter:description" content="복잡한 3D 볼륨 셰이더 렌더링을 통해 그래픽 카드 성능을 테스트하는 전문 온라인 GPU 성능 테스트 도구로, 실시간 FPS 모니터링 및 성능 분석을 지원합니다.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/og-img.avif">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/#website",
                "url": "https://volumeshaderbmtest.com/ko-kr/",
                "name": "Volume Shader BM Test",
                "description": "전문 온라인 GPU 성능 테스트 도구",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/#organization",
                "name": "Toxic Mushroom Test Team",
                "url": "https://volumeshaderbmtest.com/ko-kr/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/#software",
                "name": "Volume Shader BM Test",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/ko-kr/",
                "description": "경량, 중간, 무거움, 극한의 네 가지 테스트 모드를 지원하는 WebGL 기반 온라인 GPU 성능 벤치마크 도구로, FPS, 온도 및 기타 중요한 성능 지표를 실시간으로 모니터링합니다.",
                "featureList": [
                    "GPU 성능 테스트",
                    "실시간 FPS 모니터링",
                    "온도 상태 모니터링",
                    "성능 기록 추적",
                    "테스트 결과 분석",
                    "WebGL 볼륨 렌더링",
                    "다양한 난이도 테스트 모드"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "CNY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/#gpu-knowledge",
                "headline": "GPU 지식 데이터베이스 - 그래픽 카드를 위한 완전한 기술 가이드",
                "description": "GPU 기초, 주요 매개변수, 아키텍처 발전 및 성능에 영향을 미치는 요소들에 대한 상세한 소개로, 그래픽 카드 선택 및 성능 최적화를 위한 전문적인 가이드를 제공합니다.",
                "author": {
                    "@type": "Organization",
                    "name": "Toxic Mushroom Test Team"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "테스트 중에 내 기기가 뜨거워지는 이유는 무엇인가요?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "이는 정상입니다. Toxic Mushroom 테스트는 GPU가 최대 용량으로 작동하게 하므로 열이 발생하는 것은 불가피합니다. 온도가 너무 높으면 테스트를 중지하거나 테스트 레벨을 낮추는 것이 좋습니다."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "테스트 결과의 점수는 무엇을 의미하나요?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "점수는 FPS, 렌더링 안정성 및 테스트 레벨을 고려합니다. 높은 점수는 더 강력한 GPU 성능을 나타내며 다른 기기와 수평적으로 비교할 수 있습니다."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "브라우저에서 WebGL이 지원되지 않는다고 표시되면 어떻게 해야 하나요?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "브라우저가 최신 버전인지 확인하고, 하드웨어 가속이 활성화되어 있는지 확인하거나 다른 브라우저로 시도해보세요. 일부 모바일 기기는 WebGL 2.0을 지원하지 않을 수 있습니다."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "테스트 데이터가 업로드되나요?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "모든 테스트 데이터는 로컬에 저장되며 서버에 업로드되지 않습니다. 개인정보 보호 문제에 대해 걱정하지 않고 안전하게 테스트를 수행할 수 있습니다."
                        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/#gpu-recommendations",
                "name": "그래픽 카드 추천 가이드",
                "description": "예산과 목적에 따라 분류된 전문적인 그래픽 카드 추천",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
                        "description": "고품질 1080p 게이밍에 적합한 엔트리급 게이밍 그래픽 카드",
                        "category": "그래픽 카드",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1500",
                            "highPrice": "2000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
                        "description": "최고의 가성비 선택, 추천하는 엔트리급 그래픽 카드",
                        "category": "그래픽 카드",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1400",
                            "highPrice": "1800",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
                        "description": "고품질 1440p 게이밍을 지원하는 중급 게이밍 그래픽 카드",
                        "category": "그래픽 카드",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "3000",
                            "highPrice": "3500",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 4,
                        "name": "NVIDIA GeForce RTX 4090",
                        "description": "플래그십 그래픽 카드, 최상의 4K 게이밍 성능",
                        "category": "그래픽 카드",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "12000",
                            "highPrice": "15000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/#test-guide",
                "name": "GPU 성능 테스트 가이드",
                "description": "상세한 GPU 성능 테스트 단계 및 모범 사례",
                "image": "https://volumeshaderbmtest.com/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "WebGL 호환 브라우저"
                    },
                    {
                        "@type": "HowToSupply", 
                        "name": "전용 또는 내장 그래픽 카드"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "Volume Shader BM Test 도구"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "테스트 전 준비",
                        "text": "불필요한 프로그램과 브라우저 탭을 닫고, 기기의 열 방출 환경을 좋게 유지하세요"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "테스트 모드 선택",
                        "text": "기기 성능에 맞는 적절한 테스트 모드를 선택하세요. 가벼운 모드부터 시작하는 것이 좋습니다"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "테스트 시작",
                        "text": "테스트 시작을 클릭하고 실시간 성능 데이터 변화를 관찰하세요"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "결과 확인",
                        "text": "테스트 완료 후 상세한 보고서와 성능 권장 사항을 확인하세요"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="../static/js/js.js"></script>
    <script src="../static/js/chart.js"></script>
    <script src="../static/js/three.min.js"></script>
    <link rel="stylesheet" href="../static/css/all.min.css">
    <link href="../static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="../static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="../logo.svg" alt="Logo" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader BM Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="#test" class="active nav-link">성능 테스트</a></li>
                    <li><a href="#about" class="nav-link">도구 소개</a></li>
                    <li><a href="#test-guide" class="nav-link">테스트 가이드</a></li>
                    <li><a href="#technology" class="nav-link">기술 원리</a></li>
                    <!-- <li><a href="#compatibility" class="nav-link">호환성</a></li>
                    <li><a href="#gpu-knowledge" class="nav-link">GPU 지식</a></li>
                    <li><a href="#hardware-recommendation" class="nav-link">하드웨어 추천</a></li> -->
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="언어 변경">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">한국어</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">English</a>
                        <a href="../zh-cn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="../ja-jp/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">한국어</a>
                        <a href="../vi-vn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Tiếng Việt</a>
                        <a href="../it-it/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Italiano</a>
                        <a href="../es-es/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Español</a>
                        <a href="../fr-fr/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Français</a>
                        <a href="../de-de/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Deutsch</a>
                        <a href="../pt-pt/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Português</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="테마 변경">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="메뉴">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">성능 테스트</a></li>
                <li><a href="#about" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">도구 소개</a></li>
                <li><a href="#test-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">테스트 가이드</a></li>
                <li><a href="#technology" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">기술 원리</a></li>
                <li><a href="#compatibility" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">호환성</a></li>
                <li><a href="#gpu-knowledge" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">GPU 지식</a></li> -->
                <li><a href="#hardware-recommendation" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">하드웨어 추천</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
          
        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader BM Test</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> 전체화면
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> 스크린샷
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">렌더링을 시작하려면 테스트 시작 버튼을 클릭하세요</div>
                                <div class="lg:hidden">GPU 테스트를 시작하려면 아래 버튼을 클릭하세요</div>
                                <div class="text-sm text-gray-300 mt-1">기기가 WebGL을 지원하는지 확인하세요</div>
                            </div>
                        </div>
                        
                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                
                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">테스트 모드 선택</h3>
                                    </div>
                                    
                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">가벼운 테스트</h4>
                                                        <p class="text-gray-300 text-sm">기본 성능 테스트, 저성능 기기에 적합</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">중간 테스트</h4>
                                                        <p class="text-gray-300 text-sm">표준 부하 테스트, 중급 기기에 적합</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">무거운 테스트</h4>
                                                        <p class="text-gray-300 text-sm">고강도 테스트, 고성능 기기에 적합</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">극한 테스트</h4>
                                                        <p class="text-gray-300 text-sm">극한 스트레스 테스트, 주의해서 사용</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            테스트 시작
                                        </button>
                                        
                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            테스트 중지
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                GPU 테스트 시작
                            </button>
                        </div>
                        
                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>렌더 시간: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>삼각형: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>복잡도: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>
                        
                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">성능 문제가 감지되었습니다. 테스트 레벨을 낮추는 것을 고려하세요</span>
                        </div>
                    </div>
                    
                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">기기 정보</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">감지 중...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>WebGL 버전:</span>
                                <span id="webgl-version" class="text-right">감지 중...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>브라우저:</span>
                                <span id="browser-info" class="text-right">감지 중...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>운영체제:</span>
                                <span id="os-info" class="text-right">감지 중...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>가벼운 테스트</h3>
                                    <p>기본 성능 테스트</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>중간 테스트</h3>
                                    <p>표준 부하 테스트</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>무거운 테스트</h3>
                                    <p>고강도 테스트</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>극한 테스트</h3>
                                    <p>극한 스트레스 테스트</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>사용자 정의</h3>
                                    <p>사용자 정의 매개변수 테스트</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">테스트 제어</h3>
                        
                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> 테스트 시작
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> 테스트 중지
                            </button>
                        </div>
                        
                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>테스트 진행</span>
                                <span id="progress-text">0/60 초</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">삼각형 수</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">현재: <span id="triangle-value">50,000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">셰이더 복잡도</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">현재: <span id="complexity-value">2.5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">테스트 기간 (초)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">현재: <span id="duration-value">60</span>초</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">실시간 성능</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">점수</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">보통</div>
                                <div class="text-gray-500 dark:text-gray-400">온도 상태</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">가벼움</div>
                                <div class="text-gray-500 dark:text-gray-400">테스트 레벨</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> 결과 공유
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> 테스트 기록
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">성능 분석</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPS 차트</button>
                        <button class="chart-tab" data-chart="score">점수 추이</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">평균 FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">최대 FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">최소 FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">안정성</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">테스트 기록</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> 삭제
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>아직 테스트 기록 없음</p>
                        <p class="text-sm">완료된 성능 테스트가 여기에 표시됩니다</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        총 <span id="history-count">0</span>개 테스트
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> 내보내기
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Shader BM Introduction Section -->
        <div class="card p-6 mt-6 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                <div class="order-2 md:order-1">
                    <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM 살펴보기</h2>
                    <div class="text-gray-700 dark:text-gray-300 space-y-4">
                        <p>Volume Shader BM은 복잡한 볼륨 셰이더 렌더링을 통해 그래픽 하드웨어의 한계를 시험하도록 설계된 최첨단 GPU 벤치마크 도구입니다.</p>
                        <p>이 벤치마크는 고급 3D 볼륨 셰이더를 활용하여 GPU의 처리 능력을 테스트하고 정확한 성능 지표를 제공하는 사실적인 프랙탈 패턴을 생성합니다.</p>
                        <p>시스템 성능을 평가하려는 게이머든, 하드웨어 기능을 검증해야 하는 전문가든, 또는 단순히 기기의 그래픽 성능이 궁금한 사용자든, Volume Shader BM은 정확하고 신뢰할 수 있는 결과를 제공합니다.</p>
                        <div class="mt-6">
                            <a href="#test" class="btn-primary inline-flex items-center px-4 py-2 rounded-md">
                                <i class="fas fa-play mr-2"></i> 벤치마크 시작
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-1 md:order-2">
                    <div class="rounded-lg overflow-hidden shadow-lg transform transition-transform hover:scale-105 duration-300">
                        <img src="../images/og-image.avif" alt="Volume Shader Benchmark Visualization" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- About Tool Area -->
        <div id="about" class="card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test 소개</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Volume Shader BM Test는 복잡한 3D 볼륨 셰이더 렌더링을 통해 기기의 그래픽 성능을 평가하는 WebGL 기반 온라인 GPU 성능 평가 도구입니다.</p>
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2">테스트 레벨 설명</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm">가벼움: 10K 삼각형</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm">중간: 50K 삼각형</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                            <span class="text-sm">무거움: 200K 삼각형</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm">극한: 500K 삼각형</span>
                        </div>
                    </div>
                </div>
                <p><strong>참고:</strong> 고강도 테스트는 저성능 기기에서 지연이나 발열을 유발할 수 있습니다. 기기의 성능에 맞는 적절한 테스트 레벨을 선택해 주세요.</p>
            </div>
        </div>

        <!-- Test Guide Area -->
        <div id="test-guide" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-book-open mr-2 text-primary-500"></i>GPU 테스트 가이드
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Preparation -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-clipboard-check mr-2 text-green-500"></i>테스트 준비
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>시스템 리소스를 확보하기 위해 불필요한 프로그램과 브라우저 탭을 닫으세요</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>테스트 중 과열을 방지하기 위해 기기의 냉각 상태가 양호한지 확인하세요</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>테스트 중 중단을 방지하기 위해 안정적인 전원 공급 장치를 사용하세요</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>최적의 성능을 위해 그래픽 드라이버를 최신 버전으로 업데이트하세요</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Test Steps -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-list-ol mr-2 text-blue-500"></i>테스트 단계
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                <span>적절한 테스트 모드를 선택하세요 (가벼운 모드부터 시작하는 것이 좋습니다)</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                <span>필요한 경우 복잡도와 테스트 기간을 조정하세요</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                <span>테스트 시작을 클릭하고 실시간 성능 데이터를 관찰하세요</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                                <span>테스트 완료 후 상세 보고서와 권장 사항을 확인하세요</span>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- Test Mode Details -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-layer-group mr-2 text-purple-500"></i>테스트 모드 세부 정보
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                            <h4 class="font-medium text-green-800 dark:text-green-200">가벼운 테스트</h4>
                        </div>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• 사무용 기기 및 엔트리급 GPU에 적합</li>
                            <li>• 10K 삼각형, 낮은 GPU 부하</li>
                            <li>• 테스트 기간: 30초</li>
                            <li>• 발열이 적어 장시간 실행에 적합</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">중간 테스트</h4>
                        </div>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• 중급 게이밍 GPU에 적합</li>
                            <li>• 50K 삼각형, 중간 GPU 부하</li>
                            <li>• 테스트 기간: 60초</li>
                            <li>• 성능 병목 현상과 안정성 파악 가능</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200">무거운 테스트</h4>
                        </div>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• 하이엔드 게이밍 GPU에 적합</li>
                            <li>• 200K 삼각형, 높은 GPU 부하</li>
                            <li>• 테스트 기간: 90초</li>
                            <li>• 냉각 시스템 및 전원 공급 장치 안정성 테스트</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                            <h4 class="font-medium text-red-800 dark:text-red-200">극한 테스트</h4>
                        </div>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• 전문가용 하이엔드 GPU에 적합</li>
                            <li>• 500K 삼각형, 극한 GPU 부하</li>
                            <li>• 테스트 기간: 120초</li>
                            <li>• 스트레스 테스트, 기기 발열 가능성 있음</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Results Interpretation Guide -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-indigo-500"></i>결과 해석 가이드
                </h3>
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">FPS 분석</h4>
                            <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                                <li>• <strong>60+ FPS</strong>: 탁월한 성능</li>
                                <li>• <strong>30-60 FPS</strong>: 양호한 성능</li>
                                <li>• <strong>15-30 FPS</strong>: 평균적인 성능</li>
                                <li>• <strong>&lt;15 FPS</strong>: 낮은 성능</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">온도 상태</h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• <strong>보통</strong>: GPU 온도 안정적</li>
                                <li>• <strong>따뜻함</strong>: 약간의 온도 상승, 정상 범위</li>
                                <li>• <strong>뜨거움</strong>: 냉각 필요</li>
                                <li>• <strong>과열</strong>: 테스트 중단 권장</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">총 점수</h4>
                            <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                                <li>• <strong>9000+</strong>: 플래그십 성능</li>
                                <li>• <strong>6000-9000</strong>: 하이엔드 성능</li>
                                <li>• <strong>3000-6000</strong>: 중급 성능</li>
                                <li>• <strong>&lt;3000</strong>: 엔트리급 성능</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Important Notes -->
            <div class="mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">중요 참고 사항</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• 모바일 기기에서 고강도 테스트를 실행할 때는 배터리 잔량과 발열에 주의하세요</li>
                                <li>• 테스트 중 화면 깨짐이나 시스템 충돌이 발생하면 즉시 테스트를 중단하세요</li>
                                <li>• 테스트 결과는 참고용일 뿐이며, 실제 게임 성능은 CPU, RAM 및 기타 요소의 영향을 받습니다</li>
                                <li>• 정기적인 테스트는 GPU 성능 변화를 모니터링하고 하드웨어 문제를 조기에 감지하는 데 도움이 됩니다</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Principles Area -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">기술 원리</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>이 도구는 고급 WebGL 2.0 기술과 볼륨 셰이더 알고리즘을 사용하여 다음과 같은 기술로 GPU 성능 테스트를 구현합니다:</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>볼륨 렌더링 기술</strong>: 복잡한 수학 함수를 사용하여 독버섯의 형태와 질감을 시뮬레이션하는 3D 볼륨 텍스처 생성</li>
                    <li><strong>동적 셰이더</strong>: 테스트 레벨에 따라 프래그먼트 셰이더의 복잡도를 동적으로 조정하여 GPU 계산 부하 증가</li>
                    <li><strong>실시간 성능 모니터링</strong>: WebGL 확장 기능과 Performance API를 통해 FPS 및 렌더링 시간과 같은 주요 지표 모니터링</li>
                    <li><strong>온도 추론 알고리즘</strong>: 성능 저하 곡선을 통해 기기의 온도 상태 추론</li>
                </ul>
                <p>테스트 알고리즘은 기기의 성능 한계에 도달할 때까지 렌더링 복잡도를 점진적으로 증가시켜 GPU의 처리 능력을 정확하게 평가합니다.</p>
            </div>
        </div>

        <!-- Compatibility Area -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">브라우저 호환성</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">완전히 지원됨. 최상의 성능을 위해 최신 버전 권장.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">탁월한 성능으로 완전히 지원됨.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">기본적인 지원, 일부 고급 기능은 제한될 수 있음.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">완전히 지원됨, Chrome과 유사한 성능.</p>
                </div>
            </div>
        </div>

        <!-- GPU Knowledge Base Area -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>GPU 지식 데이터베이스
            </h2>
            
            <!-- GPU Basic Knowledge -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>GPU 기초
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">GPU란 무엇인가</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">그래픽 처리 장치(Graphics Processing Unit)로, 그래픽 렌더링과 병렬 컴퓨팅에 특화된 칩으로, 수천 개의 작은 코어가 있어 많은 간단한 작업을 동시에 처리하는 데 탁월합니다.</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPU vs CPU</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPU는 더 적지만 강력한 코어를 가지고 있어 복잡한 논리 연산에 적합하고, GPU는 많은 단순 코어를 가지고 있어 병렬 컴퓨팅과 그래픽 렌더링에 이상적입니다. 최적의 성능을 위해 함께 작동합니다.</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">GPU 유형</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">통합 GPU: CPU에 내장되어 있으며 전력 소비가 낮지만 성능이 제한적입니다. 전용 GPU: 별도의 GPU 칩으로, 강력한 성능을 제공하며 게임과 전문 작업에 적합합니다.</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Parameters Analysis -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>주요 매개변수 분석
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">비디오 메모리 (VRAM)</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 그래픽 데이터를 저장하기 위한 전용 메모리</li>
                                <li>• 용량이 고해상도 게임 성능에 영향을 미침</li>
                                <li>• 주류 게임에는 8GB+ 권장</li>
                                <li>• 4K 게임이나 전문 작업에는 12GB+ 필요</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">코어 클럭</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 기본 클럭: GPU의 안정적인 작동 주파수</li>
                                <li>• 부스트 클럭: 자동 오버클럭 후 주파수</li>
                                <li>• 높은 주파수는 더 강력한 연산 능력을 의미</li>
                                <li>• 오버클럭을 통해 더욱 향상 가능</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">스트림 프로세서</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 병렬 컴퓨팅을 수행하는 코어 유닛</li>
                                <li>• 더 많은 유닛은 더 강력한 병렬 처리 능력을 의미</li>
                                <li>• NVIDIA에서는 CUDA 코어라고 불림</li>
                                <li>• AMD에서는 스트림 프로세서(SP)라고 불림</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">버스 폭 & 대역폭</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• 메모리 버스 폭이 데이터 전송에 영향을 미침</li>
                                <li>• 256비트 이상은 하이엔드 구성</li>
                                <li>• 대역폭 = 버스 폭 × 메모리 주파수</li>
                                <li>• 높은 대역폭은 성능 병목 현상을 줄임</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU Architecture Evolution -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>GPU 아키텍처 발전
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>NVIDIA 아키텍처
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40 시리즈</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30 시리즈</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20 시리즈</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10 시리즈</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>AMD 아키텍처
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000 시리즈</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000 시리즈</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000 시리즈</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500 시리즈</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Factors Affecting GPU Performance -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>GPU 성능에 영향을 미치는 요소
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">온도 제어</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">과열은 성능 제한을 유발하고 성능을 저하시킵니다</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">전원 공급</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">불충분한 전원 공급은 GPU 성능을 제한합니다</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">드라이버</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">최신 드라이버는 게임 성능을 최적화합니다</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">시스템 구성</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPU와 RAM도 전체 성능에 영향을 미칩니다</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>GPU 추천 가이드
            </h2>
            
            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>예산 기반 추천
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">엔트리급</h4>
                            <!-- <p class="text-sm text-green-600 dark:text-green-400">￥1000-2000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">고품질 1080p 게이밍</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">추천 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">최고의 가성비 선택</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">추천 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>적합한 용도:</strong> 1080p 게이밍, 가벼운 콘텐츠 제작, 일상 사무 작업</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">중급</h4>
                            <!-- <p class="text-sm text-blue-600 dark:text-blue-400">￥2000-4000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">고품질 1440p</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">추천 ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">큰 VRAM 이점</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">추천 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>적합한 용도:</strong> 1440p 게이밍, 비디오 편집, 라이브 스트리밍</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">하이엔드</h4>
                            <!-- <p class="text-sm text-purple-600 dark:text-purple-400">￥4000-8000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">4K 게이밍 파워하우스</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">추천 ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">24GB 대용량 VRAM</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">추천 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>적합한 용도:</strong> 4K 게이밍, 전문 렌더링, AI 컴퓨팅</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">플래그십</h4>
                            <!-- <p class="text-sm text-orange-600 dark:text-orange-400">￥8000+</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">최상의 성능</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">추천 ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080 Super</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">가성비 플래그십</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">추천 ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080-super">
                                     <i class="fas fa-shopping-cart mr-1"></i>구매 보기
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>적합한 용도:</strong> 8K 게이밍, 워크스테이션, 열정가</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Usage-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-tasks mr-2 text-blue-500"></i>용도 기반 추천
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Gaming GPUs -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                            <h4 class="font-semibold text-red-800 dark:text-red-200">게이밍 GPU</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">이스포츠 게이밍</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060, RX 7600</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">높은 FPS 1080p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">AAA 타이틀</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7700 XT</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">고품질 1440p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">4K 게이밍</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">4K 울트라 설정</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Content Creation -->
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">콘텐츠 제작</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">비디오 편집</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7900 GRE</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">하드웨어 인코딩 가속</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">3D 렌더링</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">CUDA/OptiX 가속</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">라이브 스트리밍</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060 이상</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">NVENC 인코딩</div>
                                 <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                     <i class="fas fa-shopping-cart mr-1"></i>RTX 4060 시리즈 보기
                                 </a>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Professional Work -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200">전문 작업</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">AI 개발</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">높은 VRAM 요구사항</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">CAD 디자인</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, Quadro 시리즈</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">전문 드라이버 최적화</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                         <i class="fas fa-shopping-cart mr-1"></i>Quadro 시리즈
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">과학적 계산</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">더블 프레시전 부동소수점</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                         <i class="fas fa-shopping-cart mr-1"></i>A5000
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
            </div>
            
            <!-- Purchase Advice -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>구매 조언
                </h3>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                                중요 고려사항
                            </h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• 충분한 전원 공급 장치 성능 확보</li>
                                <li>• 케이스 공간 및 냉각 확인</li>
                                <li>• CPU 성능 균형 고려</li>
                                <li>• VRAM 용량 요구사항 확인</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                                <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                                최적의 구매 시기
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• 제품 출시 후 3-6개월</li>
                                <li>• 블랙 프라이데이 및 기타 쇼핑 이벤트</li>
                                <li>• 암호화폐 채굴 침체 이후</li>
                                <li>• 중고 시장의 합리적인 관찰</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                                브랜드 선택
                            </h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                                <li>• ASUS ROG: 하이엔드 게이머의 첫 번째 선택</li>
                                <li>• MSI Gaming: 균형 잡힌 가성비</li>
                                <li>• GIGABYTE AORUS: 뛰어난 냉각 성능</li>
                                <li>• Galax: 입문자를 위한 좋은 가치</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">자주 묻는 질문</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">테스트 중에 내 기기가 뜨거워지는 이유는 무엇인가요?</h3>
                    <p class="text-gray-700 dark:text-gray-300">이는 정상입니다. Toxic Mushroom 테스트는 GPU가 최대 용량으로 작동하게 하므로 열이 발생하는 것은 불가피합니다. 온도가 너무 높으면 테스트를 중지하거나 테스트 레벨을 낮추는 것이 좋습니다.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">테스트 결과의 점수는 무엇을 의미하나요?</h3>
                    <p class="text-gray-700 dark:text-gray-300">점수는 FPS, 렌더링 안정성 및 테스트 레벨을 고려합니다. 높은 점수는 더 강력한 GPU 성능을 나타내며 다른 기기와 직접 비교하는 데 사용할 수 있습니다.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">브라우저에서 WebGL이 지원되지 않는다고 표시되면 어떻게 해야 하나요?</h3>
                    <p class="text-gray-700 dark:text-gray-300">브라우저가 최신 버전인지 확인하고, 하드웨어 가속이 활성화되어 있는지 확인하거나 다른 브라우저로 시도해보세요. 일부 모바일 기기는 WebGL 2.0을 지원하지 않을 수 있습니다.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">테스트 데이터가 업로드되나요?</h3>
                    <p class="text-gray-700 dark:text-gray-300">모든 테스트 데이터는 로컬에 저장되며 서버에 업로드되지 않습니다. 개인정보 보호 문제에 대해 걱정하지 않고 안전하게 테스트를 수행할 수 있습니다.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">전문 온라인 GPU 성능 테스트 도구</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">Volume Shader BM Test</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">관련 링크</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">사용자 가이드</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">기술 지원</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">피드백</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">면책 조항</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">이 도구는 GPU 성능 테스트 전용입니다. 사용 중 기기 발열과 전력 소비는 정상적인 현상입니다. 기기의 성능에 맞는 적절한 테스트 레벨을 선택해 주세요.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../static/js/app.js"></script>
    
    <!-- 언어 선택 스크립트 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            // 언어 선택 메뉴 표시/숨김
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });
            
            // 페이지의 다른 곳을 클릭하면 드롭다운 메뉴 닫기
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>