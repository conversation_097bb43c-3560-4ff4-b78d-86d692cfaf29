// Toxic Mushroom GPU Test Tool - Core JavaScript Code

class MushroomGPUTest {
    constructor() {
        this.canvas = null;
        this.gl = null;
        this.renderer = null;
        this.scene = null;
        this.camera = null;
        this.mushroom = null;
        this.isRunning = false;
        this.testData = { fps: [], scores: [], temperatures: [] };
        this.performanceHistory = [];
        this.charts = {};
        this.currentTestMode = 'light';
        this.testStartTime = 0;
        this.testDuration = 60;
        this.frameCount = 0;
        this.lastTime = 0;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.detectDeviceInfo();
        this.initCharts();
        this.loadTestHistory();
        this.setupWebGL();
    }

    setupEventListeners() {
        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => this.toggleTheme());
        
        // Mobile menu
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileNav = document.querySelector('.mobile-nav');
        mobileMenuBtn.addEventListener('click', () => {
            mobileNav.classList.toggle('hidden');
            mobileNav.classList.toggle('active');
            mobileMenuBtn.classList.toggle('active');
        });
        
        // Close mobile menu when navigation links are clicked
        document.querySelectorAll('.mobile-nav a').forEach(link => {
            link.addEventListener('click', () => {
                mobileNav.classList.add('hidden');
                mobileNav.classList.remove('active');
                mobileMenuBtn.classList.remove('active');
            });
        });

        // Test mode switching
        document.querySelectorAll('.test-mode-card').forEach(card => {
            card.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTestMode(card);
            });
        });

        // Test control buttons
        document.getElementById('start-test-btn').addEventListener('click', () => this.startTest());
        document.getElementById('stop-test-btn').addEventListener('click', () => this.stopTest());

        // Fullscreen and screenshot
        document.getElementById('fullscreen-btn').addEventListener('click', () => this.toggleFullscreen());
        document.getElementById('screenshot-btn').addEventListener('click', () => this.takeScreenshot());

        // Chart switching
        document.querySelectorAll('.chart-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchChart(tab));
        });

        // Custom parameter sliders
        this.setupSliders();

        // History records
        document.querySelector('.clear-history-btn').addEventListener('click', () => this.clearHistory());
        document.getElementById('export-history').addEventListener('click', () => this.exportHistory());

        // Share functionality
        document.getElementById('share-btn').addEventListener('click', () => this.shareResults());
        document.getElementById('history-btn').addEventListener('click', () => this.showHistory());

        // Mobile controls
        this.setupMobileControls();
        
        // Synchronize mobile control status when window size changes
        window.addEventListener('resize', () => {
            if (this.syncMobileControls) {
                this.syncMobileControls();
            }
        });
    }

    setupTheme() {
        const htmlElement = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');
        
        if (localStorage.getItem('mushroom-theme') === 'dark' || 
            (!localStorage.getItem('mushroom-theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            htmlElement.classList.remove('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
    }

    toggleTheme() {
        const htmlElement = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');
        
        if (htmlElement.classList.contains('dark')) {
            htmlElement.classList.remove('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            localStorage.setItem('mushroom-theme', 'light');
        } else {
            htmlElement.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            localStorage.setItem('mushroom-theme', 'dark');
        }
    }

    detectDeviceInfo() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
        
        if (!gl) {
            this.showError('Your browser does not support WebGL, GPU testing cannot be performed');
            return;
        }

        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        const gpuInfo = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'Unknown GPU';
        const webglVersion = gl.getParameter(gl.VERSION);
        
        document.getElementById('gpu-info').textContent = gpuInfo;
        document.getElementById('webgl-version').textContent = webglVersion;
        document.getElementById('browser-info').textContent = this.getBrowserInfo();
        document.getElementById('os-info').textContent = this.getOSInfo();
    }

    getBrowserInfo() {
        const ua = navigator.userAgent;
        if (ua.includes('Chrome')) return 'Chrome';
        if (ua.includes('Firefox')) return 'Firefox';
        if (ua.includes('Safari')) return 'Safari';
        if (ua.includes('Edge')) return 'Edge';
        return 'Unknown Browser';
    }

    getOSInfo() {
        const ua = navigator.userAgent;
        if (ua.includes('Windows')) return 'Windows';
        if (ua.includes('Mac')) return 'macOS';
        if (ua.includes('Linux')) return 'Linux';
        if (ua.includes('Android')) return 'Android';
        if (ua.includes('iPhone') || ua.includes('iPad')) return 'iOS';
        return 'Unknown OS';
    }

    switchTestMode(card) {
        document.querySelectorAll('.test-mode-card').forEach(c => c.classList.remove('active'));
        card.classList.add('active');
        
        this.currentTestMode = card.getAttribute('data-mode');
        document.getElementById('current-level').textContent = card.querySelector('h3').textContent;
        
        const customParams = document.getElementById('custom-params');
        if (this.currentTestMode === 'custom') {
            customParams.classList.remove('hidden');
        } else {
            customParams.classList.add('hidden');
        }
    }

    setupSliders() {
        const triangleSlider = document.getElementById('triangle-slider');
        const complexitySlider = document.getElementById('complexity-slider');
        const durationSlider = document.getElementById('duration-slider');
        
        triangleSlider.addEventListener('input', (e) => {
            document.getElementById('triangle-value').textContent = parseInt(e.target.value).toLocaleString();
        });
        
        complexitySlider.addEventListener('input', (e) => {
            document.getElementById('complexity-value').textContent = parseFloat(e.target.value) + 'x';
        });
        
        durationSlider.addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            document.getElementById('duration-value').textContent = value;
            this.testDuration = value;
        });
    }

    setupWebGL() {
        this.canvas = document.getElementById('mushroom-canvas');
        this.gl = this.canvas.getContext('webgl2') || this.canvas.getContext('webgl');
        
        if (!this.gl) {
            this.showError('WebGL initialization failed');
            return;
        }

        this.renderer = new THREE.WebGLRenderer({ 
            canvas: this.canvas, 
            antialias: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        this.renderer.setClearColor(0x000000);

        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, this.canvas.clientWidth / this.canvas.clientHeight, 0.1, 1000);
        // Camera is only used for projection, actual rendering uses ray marching in the shader
        this.camera.position.z = 1;

        this.createMushroom();
        window.addEventListener('resize', () => this.onWindowResize());
    }

    createMushroom() {
        // Create fullscreen quad - exactly following the volumeshaderbm.html approach
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array([
            -1.0, -1.0, 0.0,  1.0, -1.0, 0.0,  1.0,  1.0, 0.0,
            -1.0, -1.0, 0.0,  1.0,  1.0, 0.0, -1.0,  1.0, 0.0
        ]);
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const material = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 1.0 },
                complexity: { value: 1.0 },
                right: { value: new THREE.Vector3(1, 0, 0) },
                forward: { value: new THREE.Vector3(0, 0, -1) },
                up: { value: new THREE.Vector3(0, 1, 0) },
                origin: { value: new THREE.Vector3(0, 0, 2) },
                x: { value: 1.0 },
                y: { value: 1.0 },
                len: { value: 1.6 }
            },
            vertexShader: `
                varying vec3 dir, localdir;
                uniform vec3 right, forward, up, origin;
                uniform float x, y;
                void main() {
                    gl_Position = vec4(position, 1.0);
                    dir = forward + right * position.x * x + up * position.y * y;
                    localdir.x = position.x * x;
                    localdir.y = position.y * y;
                    localdir.z = -1.0;
                }
            `,
            fragmentShader: `
                precision highp float;
                
                #define PI 3.14159265358979324
                #define M_L 0.3819660113
                #define M_R 0.6180339887
                #define MAXR 8
                #define SOLVER 8
                
                uniform float time;
                uniform float complexity;
                uniform vec3 right, forward, up, origin;
                uniform float len;
                varying vec3 dir, localdir;
                
                // Fractal kernel exactly following the original implementation
                float kernal(vec3 pos) {
                    vec3 a = pos;
                    float b, c, d, e;
                    
                    int iterations = int(complexity * 2.0 + 3.0);
                    for(int i = 0; i < 16; i++) {
                        if(i >= iterations) break;
                        
                        b = length(a);
                        c = atan(a.y, a.x) * 8.0;
                        e = 1.0 / b;
                        d = acos(clamp(a.z / b, -1.0, 1.0)) * 8.0;
                        b = pow(b, 8.0);
                        a = vec3(b * sin(d) * cos(c), b * sin(d) * sin(c), b * cos(d)) + pos;
                        if(b > 6.0) {
                            break;
                        }
                    }
                    return 4.0 - a.x * a.x - a.y * a.y - a.z * a.z;
                }
                
                void main() {
                    vec3 color = vec3(0.0);
                    int sign_hit = 0;
                    float v, v1, v2;
                    float r1, r2, r3, r4, m1, m2, m3, m4;
                    vec3 n, reflect_vec, ver;
                    
                    float step_size = 0.002 * complexity;
                    int max_steps = int(200.0 + complexity * 600.0);
                    
                    v1 = kernal(origin + dir * (step_size * len));
                    v2 = kernal(origin);
                    
                    for(int k = 2; k < 1002; k++) {
                        if(k >= max_steps) break;
                        
                        ver = origin + dir * (step_size * len * float(k));
                        v = kernal(ver);
                        
                        if(v > 0.0 && v1 < 0.0) {
                            r1 = step_size * len * float(k - 1);
                            r2 = step_size * len * float(k);
                            m1 = kernal(origin + dir * r1);
                            m2 = kernal(origin + dir * r2);
                            
                            for(int l = 0; l < SOLVER; l++) {
                                r3 = r1 * 0.5 + r2 * 0.5;
                                m3 = kernal(origin + dir * r3);
                                if(m3 > 0.0) {
                                    r2 = r3;
                                    m2 = m3;
                                } else {
                                    r1 = r3;
                                    m1 = m3;
                                }
                            }
                            
                            if(r3 < 2.0 * len) {
                                sign_hit = 1;
                                break;
                            }
                        }
                        
                        if(v < v1 && v1 > v2 && v1 < 0.0 && (v1 * 2.0 > v || v1 * 2.0 > v2)) {
                            r1 = step_size * len * float(k - 2);
                            r2 = step_size * len * (float(k) - 2.0 + 2.0 * M_L);
                            r3 = step_size * len * (float(k) - 2.0 + 2.0 * M_R);
                            r4 = step_size * len * float(k);
                            m2 = kernal(origin + dir * r2);
                            m3 = kernal(origin + dir * r3);
                            
                            for(int l = 0; l < MAXR; l++) {
                                if(m2 > m3) {
                                    r4 = r3;
                                    r3 = r2;
                                    r2 = r4 * M_L + r1 * M_R;
                                    m3 = m2;
                                    m2 = kernal(origin + dir * r2);
                                } else {
                                    r1 = r2;
                                    r2 = r3;
                                    r3 = r4 * M_R + r1 * M_L;
                                    m2 = m3;
                                    m3 = kernal(origin + dir * r3);
                                }
                            }
                            
                            if(m2 > 0.0) {
                                r1 = step_size * len * float(k - 2);
                                r2 = r2;
                                m1 = kernal(origin + dir * r1);
                                m2 = kernal(origin + dir * r2);
                                
                                for(int l = 0; l < SOLVER; l++) {
                                    r3 = r1 * 0.5 + r2 * 0.5;
                                    m3 = kernal(origin + dir * r3);
                                    if(m3 > 0.0) {
                                        r2 = r3;
                                        m2 = m3;
                                    } else {
                                        r1 = r3;
                                        m1 = m3;
                                    }
                                }
                                
                                if(r3 < 2.0 * len && r3 > step_size * len) {
                                    sign_hit = 1;
                                    break;
                                }
                            } else if(m3 > 0.0) {
                                r1 = step_size * len * float(k - 2);
                                r2 = r3;
                                m1 = kernal(origin + dir * r1);
                                m2 = kernal(origin + dir * r2);
                                
                                for(int l = 0; l < SOLVER; l++) {
                                    r3 = r1 * 0.5 + r2 * 0.5;
                                    m3 = kernal(origin + dir * r3);
                                    if(m3 > 0.0) {
                                        r2 = r3;
                                        m2 = m3;
                                    } else {
                                        r1 = r3;
                                        m1 = m3;
                                    }
                                }
                                
                                if(r3 < 2.0 * len && r3 > step_size * len) {
                                    sign_hit = 1;
                                    break;
                                }
                            }
                        }
                        
                        v2 = v1;
                        v1 = v;
                    }
                    
                    if(sign_hit == 1) {
                        ver = origin + dir * r3;
                        r1 = ver.x * ver.x + ver.y * ver.y + ver.z * ver.z;
                        
                        n.x = kernal(ver - right * (r3 * 0.00025)) - kernal(ver + right * (r3 * 0.00025));
                        n.y = kernal(ver - up * (r3 * 0.00025)) - kernal(ver + up * (r3 * 0.00025));
                        n.z = kernal(ver + forward * (r3 * 0.00025)) - kernal(ver - forward * (r3 * 0.00025));
                        r3 = n.x * n.x + n.y * n.y + n.z * n.z;
                        n = n * (1.0 / sqrt(r3));
                        
                        ver = localdir;
                        r3 = ver.x * ver.x + ver.y * ver.y + ver.z * ver.z;
                        ver = ver * (1.0 / sqrt(r3));
                        reflect_vec = n * (-2.0 * dot(ver, n)) + ver;
                        
                        r3 = reflect_vec.x * 0.276 + reflect_vec.y * 0.920 + reflect_vec.z * 0.276;
                        r4 = n.x * 0.276 + n.y * 0.920 + n.z * 0.276;
                        r3 = max(0.0, r3);
                        r3 = r3 * r3 * r3 * r3;
                        r3 = r3 * 0.45 + r4 * 0.25 + 0.3;
                        
                        n.x = sin(r1 * 10.0 + time) * 0.5 + 0.5;
                        n.y = sin(r1 * 10.0 + 2.05 + time) * 0.5 + 0.5;
                        n.z = sin(r1 * 10.0 - 2.05 + time) * 0.5 + 0.5;
                        
                        color = n * r3;
                    }
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `,
            side: THREE.DoubleSide
        });

        this.mushroom = new THREE.Mesh(geometry, material);
        this.scene.add(this.mushroom);
        
        // 设置相机控制参数
        this.cameraAngle1 = 2.8;
        this.cameraAngle2 = 0.4;
        this.cameraLen = 1.6;
        this.cameraCenter = new THREE.Vector3(0, 0, 0);
    }

    startTest() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.testStartTime = performance.now();
        this.frameCount = 0;
        this.testData = { fps: [], scores: [], temperatures: [] };
        
        document.getElementById('start-test-btn').disabled = true;
        document.getElementById('start-test-btn').classList.add('opacity-50');
        document.getElementById('stop-test-btn').disabled = false;
        document.getElementById('stop-test-btn').classList.remove('opacity-50');
        document.getElementById('test-progress').classList.remove('hidden');
        document.getElementById('render-overlay').style.display = 'none';
        document.getElementById('performance-hud').classList.remove('hidden');
        
        const testParams = this.getTestParameters();
        this.updateShaderComplexity(testParams.complexity);
        this.updateGeometry(testParams.triangles);
        
        this.animate();
        this.showToast('GPU test started, please do not close the page');
        
        // Synchronize mobile control status
        if (this.syncMobileControls) {
            this.syncMobileControls();
        }
    }

    stopTest() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        
        document.getElementById('start-test-btn').disabled = false;
        document.getElementById('start-test-btn').classList.remove('opacity-50');
        document.getElementById('stop-test-btn').disabled = true;
        document.getElementById('stop-test-btn').classList.add('opacity-50');
        document.getElementById('test-progress').classList.add('hidden');
        document.getElementById('render-overlay').style.display = 'flex';
        document.getElementById('performance-hud').classList.add('hidden');
        
        this.calculateTestResults();
        this.showToast('Test stopped, results saved');
        
        // Synchronize mobile control status
        if (this.syncMobileControls) {
            this.syncMobileControls();
        }
    }

    getTestParameters() {
        const params = {
            light: { triangles: 10000, complexity: 1.0, duration: 30 },
            medium: { triangles: 50000, complexity: 2.5, duration: 60 },
            heavy: { triangles: 200000, complexity: 5.0, duration: 90 },
            extreme: { triangles: 500000, complexity: 10.0, duration: 120 },
            custom: {
                triangles: parseInt(document.getElementById('triangle-slider').value),
                complexity: parseFloat(document.getElementById('complexity-slider').value),
                duration: parseInt(document.getElementById('duration-slider').value)
            }
        };
        return params[this.currentTestMode];
    }

    updateShaderComplexity(complexity) {
        if (this.mushroom && this.mushroom.material.uniforms) {
            this.mushroom.material.uniforms.complexity.value = complexity;
            document.getElementById('complexity-level').textContent = complexity + 'x';
        }
    }

    updateGeometry(triangleCount) {
        // 根据三角面数更新球体分辨率
        const segments = Math.min(100, Math.max(8, Math.sqrt(triangleCount / 50)));
        const newGeometry = new THREE.SphereGeometry(1, segments, segments / 2);
        
        if (this.mushroom) {
            this.mushroom.geometry.dispose();
            this.mushroom.geometry = newGeometry;
        }
        
        document.getElementById('triangle-count').textContent = triangleCount.toLocaleString();
    }

    animate() {
        if (!this.isRunning) return;
        
        requestAnimationFrame(() => this.animate());
        
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        if (this.mushroom && this.mushroom.material.uniforms) {
            this.mushroom.material.uniforms.time.value = currentTime * 0.001;
            
            // 更新相机参数 - 完全按照原始实现
            this.cameraAngle1 += 0.01;
            
            const cx = 800, cy = 600;
            this.mushroom.material.uniforms.x.value = cx * 2.0 / (cx + cy);
            this.mushroom.material.uniforms.y.value = cy * 2.0 / (cx + cy);
            this.mushroom.material.uniforms.len.value = this.cameraLen;
            
            // 计算相机位置和方向
            const origin = new THREE.Vector3(
                this.cameraLen * Math.cos(this.cameraAngle1) * Math.cos(this.cameraAngle2) + this.cameraCenter.x,
                this.cameraLen * Math.sin(this.cameraAngle2) + this.cameraCenter.y,
                this.cameraLen * Math.sin(this.cameraAngle1) * Math.cos(this.cameraAngle2) + this.cameraCenter.z
            );
            
            const right = new THREE.Vector3(Math.sin(this.cameraAngle1), 0, -Math.cos(this.cameraAngle1));
            const up = new THREE.Vector3(
                -Math.sin(this.cameraAngle2) * Math.cos(this.cameraAngle1),
                Math.cos(this.cameraAngle2),
                -Math.sin(this.cameraAngle2) * Math.sin(this.cameraAngle1)
            );
            const forward = new THREE.Vector3(
                -Math.cos(this.cameraAngle1) * Math.cos(this.cameraAngle2),
                -Math.sin(this.cameraAngle2),
                -Math.sin(this.cameraAngle1) * Math.cos(this.cameraAngle2)
            );
            
            this.mushroom.material.uniforms.origin.value.copy(origin);
            this.mushroom.material.uniforms.right.value.copy(right);
            this.mushroom.material.uniforms.up.value.copy(up);
            this.mushroom.material.uniforms.forward.value.copy(forward);
        }
        
        if (this.mushroom) {
            this.mushroom.rotation.x += 0.01;
            this.mushroom.rotation.y += 0.02;
        }
        
        this.renderer.render(this.scene, this.camera);
        this.updatePerformanceData(deltaTime);
        
        const elapsed = (currentTime - this.testStartTime) / 1000;
        if (elapsed >= this.testDuration) {
            this.stopTest();
        }
    }

    updatePerformanceData(deltaTime) {
        this.frameCount++;
        const fps = Math.round(1000 / deltaTime);
        
        document.getElementById('fps-display').textContent = fps;
        document.getElementById('current-fps').textContent = fps;
        document.getElementById('render-time').textContent = Math.round(deltaTime) + 'ms';
        
        this.testData.fps.push(fps);
        
        const complexity = this.getTestParameters().complexity;
        const score = Math.round(fps * complexity * 10);
        this.testData.scores.push(score);
        document.getElementById('current-score').textContent = score;
        
        const avgFps = this.testData.fps.reduce((a, b) => a + b, 0) / this.testData.fps.length;
        const tempStatus = fps < avgFps * 0.8 ? 'Hot' : fps < avgFps * 0.9 ? 'Warm' : 'Normal';
        document.getElementById('current-temp').textContent = tempStatus;
        
        const elapsed = (performance.now() - this.testStartTime) / 1000;
        const progress = Math.min((elapsed / this.testDuration) * 100, 100);
        document.getElementById('progress-bar').style.width = progress + '%';
        document.getElementById('progress-text').textContent = `${Math.round(elapsed)}/${this.testDuration}s`;
        
        // More reasonable performance warning logic
        if (this.testData.fps.length > 10) {
            const recentFps = this.testData.fps.slice(-10);
            const avgRecentFps = recentFps.reduce((a, b) => a + b, 0) / recentFps.length;
            
            if (avgRecentFps < 30) {
                this.showWarning('Performance issue detected, low frame rate');
            } else if (avgRecentFps < 20) {
                this.showWarning('Serious performance issue detected, consider lowering test level');
            }
        }
        
        if (this.frameCount % 10 === 0) {
            this.updateCharts();
        }
    }

    calculateTestResults() {
        if (this.testData.fps.length === 0) return;
        
        const avgFps = this.testData.fps.reduce((a, b) => a + b, 0) / this.testData.fps.length;
        const maxFps = Math.max(...this.testData.fps);
        const minFps = Math.min(...this.testData.fps);
        const avgScore = this.testData.scores.reduce((a, b) => a + b, 0) / this.testData.scores.length;
        
        const variance = this.testData.fps.reduce((a, b) => a + Math.pow(b - avgFps, 2), 0) / this.testData.fps.length;
        const stability = Math.max(0, 100 - Math.sqrt(variance));
        
        document.getElementById('avg-fps').textContent = Math.round(avgFps);
        document.getElementById('max-fps').textContent = maxFps;
        document.getElementById('min-fps').textContent = minFps;
        document.getElementById('stability').textContent = Math.round(stability) + '%';
        
        const testResult = {
            timestamp: Date.now(),
            mode: this.currentTestMode,
            avgFps: Math.round(avgFps),
            maxFps: maxFps,
            minFps: minFps,
            score: Math.round(avgScore),
            stability: Math.round(stability),
            duration: this.testDuration,
            deviceInfo: {
                gpu: document.getElementById('gpu-info').textContent,
                browser: document.getElementById('browser-info').textContent,
                os: document.getElementById('os-info').textContent
            }
        };
        
        this.performanceHistory.push(testResult);
        this.saveTestHistory();
        this.updateHistoryDisplay();
    }

    initCharts() {
        const fpsCtx = document.getElementById('fpsChart').getContext('2d');
        this.charts.fps = new Chart(fpsCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'FPS',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: { y: { beginAtZero: true, max: 120 } },
                plugins: { legend: { display: false } }
            }
        });

        const scoreCtx = document.getElementById('scoreChart').getContext('2d');
        this.charts.score = new Chart(scoreCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Score',
                    data: [],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: { y: { beginAtZero: true } },
                plugins: { legend: { display: false } }
            }
        });
    }

    updateCharts() {
        const maxPoints = 50;
        
        if (this.testData.fps.length > 0) {
            const fpsData = this.testData.fps.slice(-maxPoints);
            const labels = fpsData.map((_, i) => i + 1);
            
            this.charts.fps.data.labels = labels;
            this.charts.fps.data.datasets[0].data = fpsData;
            this.charts.fps.update('none');
        }
        
        if (this.testData.scores.length > 0) {
            const scoreData = this.testData.scores.slice(-maxPoints);
            const labels = scoreData.map((_, i) => i + 1);
            
            this.charts.score.data.labels = labels;
            this.charts.score.data.datasets[0].data = scoreData;
            this.charts.score.update('none');
        }
    }

    switchChart(tab) {
        document.querySelectorAll('.chart-tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        const chartType = tab.getAttribute('data-chart');
        document.querySelectorAll('.chart-wrapper').forEach(wrapper => {
            wrapper.style.display = 'none';
        });
        document.getElementById(chartType + '-chart-wrapper').style.display = 'block';
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;
        
        this.camera.aspect = this.canvas.clientWidth / this.canvas.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
    }

    toggleFullscreen() {
        const container = document.getElementById('canvas-container');
        
        if (!document.fullscreenElement) {
            container.requestFullscreen().then(() => {
                document.getElementById('fullscreen-btn').innerHTML = '<i class="fas fa-compress mr-1"></i> Exit Fullscreen';
            });
        } else {
            document.exitFullscreen().then(() => {
                document.getElementById('fullscreen-btn').innerHTML = '<i class="fas fa-expand mr-1"></i> Fullscreen';
            });
        }
    }

    takeScreenshot() {
        if (!this.renderer) return;
        
        const dataURL = this.renderer.domElement.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = 'ToxicMushroom_Test_Screenshot.png';
        link.href = dataURL;
        link.click();
        
        this.showToast('Screenshot saved');
    }

    loadTestHistory() {
        const saved = localStorage.getItem('mushroom-test-history');
        if (saved) {
            this.performanceHistory = JSON.parse(saved);
            this.updateHistoryDisplay();
        }
    }

    saveTestHistory() {
        localStorage.setItem('mushroom-test-history', JSON.stringify(this.performanceHistory));
    }

    updateHistoryDisplay() {
        const historyItems = document.getElementById('history-items');
        const emptyLog = document.querySelector('.empty-log');
        const historyCount = document.getElementById('history-count');
        
        if (this.performanceHistory.length > 0) {
            emptyLog.style.display = 'none';
            historyItems.classList.remove('hidden');
            historyItems.innerHTML = '';
            
            this.performanceHistory.slice(-10).reverse().forEach(result => {
                const item = document.createElement('div');
                item.className = 'p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50';
                
                item.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="font-medium text-primary-600 dark:text-primary-400">${result.score} pts</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">${result.mode} | Avg ${result.avgFps} FPS</span>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">${this.formatDate(result.timestamp)}</div>
                    </div>
                `;
                
                historyItems.appendChild(item);
            });
            
            historyCount.textContent = this.performanceHistory.length;
        } else {
            emptyLog.style.display = 'flex';
            historyItems.classList.add('hidden');
            historyCount.textContent = '0';
        }
    }

    formatDate(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    clearHistory() {
        if (confirm('Are you sure you want to clear all test history?')) {
            this.performanceHistory = [];
            localStorage.removeItem('mushroom-test-history');
            this.updateHistoryDisplay();
            this.showToast('History cleared');
        }
    }

    exportHistory() {
        if (this.performanceHistory.length === 0) {
            alert('No test records available for export');
            return;
        }
        
        const csv = [
            ['Time', 'Test Mode', 'Average FPS', 'Max FPS', 'Min FPS', 'Score', 'Stability', 'GPU', 'Browser', 'OS'].join(','),
            ...this.performanceHistory.map(result => [
                this.formatDate(result.timestamp),
                result.mode,
                result.avgFps,
                result.maxFps,
                result.minFps,
                result.score,
                result.stability + '%',
                result.deviceInfo.gpu,
                result.deviceInfo.browser,
                result.deviceInfo.os
            ].join(','))
        ].join('\n');
        
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'ToxicMushroom_Test_History.csv';
        link.click();
        
        this.showToast('Test records exported');
    }

    shareResults() {
        if (this.performanceHistory.length === 0) {
            alert('No test results available to share');
            return;
        }
        
        const latest = this.performanceHistory[this.performanceHistory.length - 1];
        const shareText = `My GPU Test Result: ${latest.score} points (${latest.mode} mode, average ${latest.avgFps} FPS) - Toxic Mushroom GPU Test`;
        
        if (navigator.share) {
            navigator.share({
                title: 'Toxic Mushroom GPU Test Results',
                text: shareText,
                url: window.location.href
            });
        } else {
            navigator.clipboard.writeText(shareText).then(() => {
                this.showToast('Test results copied to clipboard');
            });
        }
    }

    showHistory() {
        document.querySelector('.history-log').scrollIntoView({ behavior: 'smooth' });
    }

    showError(message) {
        const overlay = document.getElementById('render-overlay');
        overlay.innerHTML = `
            <div class="text-center text-white">
                <i class="fas fa-exclamation-triangle text-4xl mb-2 text-red-500"></i>
                <div class="text-red-300">${message}</div>
            </div>
        `;
    }

    showWarning(message) {
        const warning = document.getElementById('warning-overlay');
        const warningText = document.getElementById('warning-text');
        warningText.textContent = message;
        warning.classList.remove('hidden');
        
        setTimeout(() => warning.classList.add('hidden'), 5000);
    }

    showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'fixed top-20 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity duration-300 opacity-0';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => toast.classList.add('opacity-100'), 10);
        
        setTimeout(() => {
            toast.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }

    setupMobileControls() {
        const showMobileControlsBtn = document.getElementById('show-mobile-controls');
        const mobileControlsOverlay = document.getElementById('mobile-controls-overlay');
        const closeMobileControlsBtn = document.getElementById('close-mobile-controls');
        const mobileStartTestBtn = document.getElementById('mobile-start-test-btn');
        const mobileStopTestBtn = document.getElementById('mobile-stop-test-btn');
        const mobileQuickStart = document.getElementById('mobile-quick-start');

        // Show mobile control panel
        showMobileControlsBtn.addEventListener('click', () => {
            mobileControlsOverlay.style.display = 'block';
            mobileQuickStart.style.display = 'none';
        });

        // Close mobile control panel
        closeMobileControlsBtn.addEventListener('click', () => {
            mobileControlsOverlay.style.display = 'none';
            if (!this.isRunning) {
                mobileQuickStart.style.display = 'block';
            }
        });

        // Mobile test mode selection
        document.querySelectorAll('.mobile-test-mode-card').forEach(card => {
            card.addEventListener('click', () => {
                // Remove all active states
                document.querySelectorAll('.mobile-test-mode-card').forEach(c => {
                    c.classList.remove('active');
                    const checkIcon = c.querySelector('.fa-check-circle');
                    const circleIcon = c.querySelector('.fa-circle');
                    if (checkIcon && circleIcon) {
                        checkIcon.classList.add('hidden');
                        circleIcon.classList.remove('hidden');
                    }
                });

                // Add active state to current card
                card.classList.add('active');
                const checkIcon = card.querySelector('.fa-check-circle');
                const circleIcon = card.querySelector('.fa-circle');
                if (checkIcon && circleIcon) {
                    checkIcon.classList.remove('hidden');
                    circleIcon.classList.add('hidden');
                }

                // Synchronize desktop test mode
                const mode = card.getAttribute('data-mode');
                this.currentTestMode = mode;
                
                // Synchronize desktop selection state
                document.querySelectorAll('.test-mode-card').forEach(desktopCard => {
                    desktopCard.classList.remove('active');
                    if (desktopCard.getAttribute('data-mode') === mode) {
                        desktopCard.classList.add('active');
                    }
                });
            });
        });

        // Mobile start test button
        mobileStartTestBtn.addEventListener('click', () => {
            mobileControlsOverlay.style.display = 'none';
            mobileQuickStart.style.display = 'none';
            this.startTest();
        });

        // Mobile stop test button
        mobileStopTestBtn.addEventListener('click', () => {
            this.stopTest();
            mobileControlsOverlay.style.display = 'none';
            mobileQuickStart.style.display = 'block';
        });

        // Listen for test status changes, synchronize button states
        this.syncMobileControls = () => {
            // Check if mobile device
            const isMobile = window.innerWidth < 1024; // lg breakpoint
            
            if (this.isRunning) {
                // Test in progress
                mobileStartTestBtn.disabled = true;
                mobileStartTestBtn.classList.add('opacity-50');
                mobileStopTestBtn.disabled = false;
                mobileStopTestBtn.classList.remove('opacity-50');
                if (isMobile) {
                    mobileQuickStart.style.display = 'none';
                }
            } else {
                // Test stopped
                mobileStartTestBtn.disabled = false;
                mobileStartTestBtn.classList.remove('opacity-50');
                mobileStopTestBtn.disabled = true;
                mobileStopTestBtn.classList.add('opacity-50');
                
                // Only show quick start button on mobile when overlay is closed
                if (isMobile && mobileControlsOverlay.style.display === 'none') {
                    mobileQuickStart.style.display = 'block';
                } else if (!isMobile) {
                    // Ensure mobile buttons are not shown on desktop
                    mobileQuickStart.style.display = 'none';
                }
            }
        };
    }
}

// Initialize application
document.addEventListener('DOMContentLoaded', () => {
    new MushroomGPUTest();
});